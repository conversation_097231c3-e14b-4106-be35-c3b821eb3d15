#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Integration module for vector-based intent detection with existing response generator
"""

from vector_intent_detector import VectorIntentDetector
from typing import Dict, Tuple
import json


class IntentIntegration:
    """Integration layer between vector intent detection and response generation"""

    # Shared vector detector instance
    _shared_vector_detector = None

    def __init__(self):
        """Initialize the integration layer"""
        if IntentIntegration._shared_vector_detector is None:
            IntentIntegration._shared_vector_detector = VectorIntentDetector()
        self.vector_detector = IntentIntegration._shared_vector_detector

        # Mapping from vector intents to existing response generator intents
        self.intent_mapping = {
            "GREETINGS": "greeting",
            "ASK_COMPANY_INFO": "ask_company_info",
            "ASK_PLATFORM_USAGE": "ask_platform_usage",
            "ASK_JOB_OPPORTUNITIES": "search_jobs",
            "ASK_JOB_DETAILS": "filter_jobs",
            "SHARE_PROFILE": "cv_feedback",
            "NOT_INTERESTED": "off_topic",
            "SALARY_EXPECTATION": "salary_query",
            "FEEDBACK_ON_JOB_MATCH": "thank_you",
            "FOLLOW_UP": "contextual_followup",
            "OTHER": "off_topic"
        }

        # Confidence thresholds for different intents
        self.confidence_thresholds = {
            "GREETINGS": 0.4,
            "ASK_COMPANY_INFO": 0.5,
            "ASK_PLATFORM_USAGE": 0.4,
            "ASK_JOB_OPPORTUNITIES": 0.5,
            "ASK_JOB_DETAILS": 0.5,
            "SHARE_PROFILE": 0.4,
            "NOT_INTERESTED": 0.4,
            "SALARY_EXPECTATION": 0.5,
            "FEEDBACK_ON_JOB_MATCH": 0.4,
            "FOLLOW_UP": 0.3,
            "OTHER": 0.2
        }

    def detect_intent_with_fallback(self, user_input: str) -> Tuple[str, float, Dict[str, float]]:
        """
        Detect intent using vector similarity with fallback to existing logic

        Returns:
            Tuple of (mapped_intent, confidence, all_similarities)
        """
        # Get vector-based intent detection
        vector_intent, confidence, all_similarities = self.vector_detector.get_best_intent(
            user_input)

        # Check if confidence meets threshold
        threshold = self.confidence_thresholds.get(vector_intent, 0.3)

        if confidence >= threshold:
            # Map to existing intent system
            mapped_intent = self.intent_mapping.get(vector_intent, "off_topic")
        else:
            # Fallback to rule-based detection for low confidence
            mapped_intent = self._fallback_intent_detection(user_input)
            confidence = 0.5  # Default confidence for fallback

        return mapped_intent, confidence, all_similarities

    def _fallback_intent_detection(self, user_input: str) -> str:
        """Fallback rule-based intent detection for low confidence cases"""
        user_input_lower = user_input.lower()

        # Simple keyword-based fallback rules
        if any(word in user_input_lower for word in ['hello', 'hi', 'chào', 'xin chào']):
            return "greeting"
        elif any(word in user_input_lower for word in ['company', 'công ty', 'fois']):
            return "ask_company_info"
        elif any(word in user_input_lower for word in ['job', 'work', 'việc làm', 'tuyển dụng']):
            return "search_jobs"
        elif any(word in user_input_lower for word in ['salary', 'lương', 'pay', 'wage']):
            return "salary_query"
        elif any(word in user_input_lower for word in ['cv', 'resume', 'profile', 'hồ sơ']):
            return "cv_feedback"
        else:
            return "off_topic"

    def get_intent_analysis(self, user_input: str) -> Dict:
        """Get detailed intent analysis including vector similarities"""
        mapped_intent, confidence, all_similarities = self.detect_intent_with_fallback(
            user_input)

        # Sort similarities for better display
        sorted_similarities = sorted(
            all_similarities.items(), key=lambda x: x[1], reverse=True)

        return {
            "user_input": user_input,
            "detected_intent": mapped_intent,
            "confidence": confidence,
            "vector_similarities": {
                "top_3": sorted_similarities[:3],
                "all": all_similarities
            },
            "intent_mapping_used": True if confidence >= 0.3 else False
        }


def test_intent_integration():
    """Test the intent integration with various inputs"""
    print("🧪 Testing Intent Integration")
    print("=" * 40)

    integration = IntentIntegration()

    test_cases = [
        "Hello, how are you?",
        "Tell me about FOIS ICT PRO",
        "What job opportunities do you have?",
        "I want to share my CV",
        "What is the expected salary?",
        "How do I use this platform?",
        "I'm not interested in this position",
        "Can you give me more details about the job?",
        "Thank you for the job recommendation",
        "What's the weather like today?",  # Should be off_topic
        "Xin chào, tôi muốn tìm việc",  # Vietnamese greeting + job search
        "Công ty FOIS làm gì?",  # Vietnamese company question
    ]

    print("🎯 Test Results:")
    print("-" * 15)

    for i, test_input in enumerate(test_cases, 1):
        print(f"\n{i}. Input: '{test_input}'")

        analysis = integration.get_intent_analysis(test_input)

        print(f"   Detected Intent: {analysis['detected_intent']}")
        print(f"   Confidence: {analysis['confidence']:.4f}")
        print(f"   Vector-based: {analysis['intent_mapping_used']}")

        # Show top 2 vector similarities
        top_similarities = analysis['vector_similarities']['top_3'][:2]
        print("   Top Vector Matches:")
        for intent, score in top_similarities:
            mapped = integration.intent_mapping.get(intent, intent)
            print(f"     - {intent} → {mapped}: {score:.4f}")

    print("\n" + "=" * 40)
    print("🎉 Intent integration test completed!")

    return integration


def compare_with_existing_system():
    """Compare vector-based detection with existing keyword-based system"""
    print("\n🔍 Comparison with Existing System")
    print("=" * 35)

    integration = IntentIntegration()

    # Test cases that might be challenging for keyword-based systems
    challenging_cases = [
        {
            "input": "I'm looking for career opportunities in your organization",
            "expected_vector": "search_jobs",
            "description": "Indirect job search"
        },
        {
            "input": "Could you provide information about your business?",
            "expected_vector": "ask_company_info",
            "description": "Formal company inquiry"
        },
        {
            "input": "I'd like to submit my professional background",
            "expected_vector": "cv_feedback",
            "description": "Formal CV submission"
        },
        {
            "input": "What compensation can I expect?",
            "expected_vector": "salary_query",
            "description": "Formal salary inquiry"
        },
        {
            "input": "Good day, I hope you're doing well",
            "expected_vector": "greeting",
            "description": "Formal greeting"
        }
    ]

    print("🎯 Challenging Cases Analysis:")
    print("-" * 30)

    for i, case in enumerate(challenging_cases, 1):
        user_input = case["input"]
        expected = case["expected_vector"]
        description = case["description"]

        print(f"\n{i}. {description}")
        print(f"   Input: '{user_input}'")
        print(f"   Expected: {expected}")

        analysis = integration.get_intent_analysis(user_input)
        detected = analysis['detected_intent']
        confidence = analysis['confidence']

        print(f"   Vector Result: {detected} (confidence: {confidence:.4f})")

        # Simple keyword fallback for comparison
        fallback = integration._fallback_intent_detection(user_input)
        print(f"   Keyword Fallback: {fallback}")

        if detected == expected:
            print("   ✅ Vector detection: CORRECT")
        else:
            print("   ⚠️ Vector detection: DIFFERENT")

        if fallback == expected:
            print("   ✅ Keyword fallback: CORRECT")
        else:
            print("   ⚠️ Keyword fallback: DIFFERENT")


if __name__ == "__main__":
    print("🚀 Intent Integration Testing")
    print("=" * 50)

    # Test the integration
    integration = test_intent_integration()

    # Compare with existing system
    compare_with_existing_system()

    print("\n💡 Integration Summary:")
    print("=" * 25)
    print("✅ Vector-based intent detection implemented")
    print("✅ Fallback to keyword-based detection for low confidence")
    print("✅ Mapping to existing response generator intents")
    print("✅ Confidence thresholds for different intent types")
    print("✅ Detailed analysis and debugging information")

    print("\n🎯 Next Steps:")
    print("- Integrate with new_response_generator.py")
    print("- Add vector intent detection to web_app.py")
    print("- Fine-tune confidence thresholds based on testing")
    print("- Add more intent descriptions for better accuracy")
