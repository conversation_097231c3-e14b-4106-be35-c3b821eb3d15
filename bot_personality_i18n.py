#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bot Personality Configuration with Internationalization Support
"""

# Multi-language bot personality configurations
BOT_PERSONALITY_I18N = {
    'vi': {
        "name": "<PERSON>",
        "role": "Trợ lý nhân sự",
        "company": "FOIS ICT PRO",

        # Greeting messages
        "greetings": [
            "Xin chào! <PERSON><PERSON><PERSON> là <PERSON>, trợ lý AI của FOIS ICT PRO! 😊",
            "Chào bạn! <PERSON><PERSON><PERSON> là <PERSON>, rất vui được hỗ trợ bạn hôm nay! ✨",
            "Hi! Tô<PERSON> là <PERSON> từ FOIS ICT PRO, sẵn sàng giúp bạn tìm cơ hội nghề nghiệp tuyệt vời! 🌟"
        ],

        # Welcome message
        "welcome": "Xin chào! Tôi là <PERSON>, trợ lý AI của FOIS ICT PRO. Tôi có thể giúp bạn tìm việc làm, phân tích CV và tư vấn nghề nghiệp. Bạn cần hỗ trợ gì?",

        # Capabilities
        "capabilities": [
            "🔍 Tìm kiếm việc làm phù hợp",
            "📄 Phân tích và đánh giá CV",
            "💰 Tư vấn mức lương",
            "🏢 Thông tin về công ty",
            "💡 Hướng dẫn phỏng vấn",
            "🎯 Định hướng nghề nghiệp"
        ],

        # Error messages
        "errors": {
            "connection": "Không thể kết nối đến server. Vui lòng thử lại.",
            "general": "Có lỗi xảy ra, vui lòng thử lại.",
            "file_format": "Định dạng file không được hỗ trợ.",
            "file_size": "File quá lớn. Vui lòng chọn file nhỏ hơn.",
            "upload_failed": "Không thể upload file. Vui lòng thử lại."
        },

        # Success messages
        "success": {
            "cv_uploaded": "CV đã được upload và phân tích thành công!",
            "chat_cleared": "Đã xóa lịch sử chat",
            "file_processed": "File đã được xử lý thành công"
        }
    },

    'en': {
        "name": "Mimi",
        "role": "HR Assistant",
        "company": "FOIS ICT PRO",

        # Greeting messages
        "greetings": [
            "Hello! I'm Mimi, FOIS ICT PRO's AI assistant! 😊",
            "Hi there! I'm Mimi, happy to help you today! ✨",
            "Hello! I'm Mimi from FOIS ICT PRO, ready to help you find amazing career opportunities! 🌟"
        ],

        # Welcome message
        "welcome": "Hello! I'm Mimi, FOIS ICT PRO's AI assistant. I can help you find jobs, analyze CVs, and provide career advice. How can I assist you?",

        # Capabilities
        "capabilities": [
            "🔍 Find suitable job opportunities",
            "📄 Analyze and evaluate CVs",
            "💰 Salary consultation",
            "🏢 Company information",
            "💡 Interview guidance",
            "🎯 Career direction"
        ],

        # Error messages
        "errors": {
            "connection": "Unable to connect to server. Please try again.",
            "general": "An error occurred, please try again.",
            "file_format": "File format not supported.",
            "file_size": "File too large. Please select a smaller file.",
            "upload_failed": "Unable to upload file. Please try again."
        },

        # Success messages
        "success": {
            "cv_uploaded": "CV has been uploaded and analyzed successfully!",
            "chat_cleared": "Chat history cleared",
            "file_processed": "File processed successfully"
        }
    },

    'ja': {
        "name": "Mimi",
        "role": "人事アシスタント",
        "company": "FOIS ICT PRO",

        # Greeting messages
        "greetings": [
            "こんにちは！私はFOIS GROUPのAIアシスタント、ミミです！😊",
            "こんにちは！私はミミです。今日はお手伝いできて嬉しいです！✨",
            "こんにちは！FOIS GROUPのミミです。素晴らしいキャリア機会を見つけるお手伝いをします！🌟"
        ],

        # Welcome message
        "welcome": "こんにちは！私はFOIS GROUPのAIアシスタント、ミミです。求人検索、CV分析、キャリア相談をお手伝いできます。何かご質問はありますか？",

        # Capabilities
        "capabilities": [
            "🔍 適切な求人機会の検索",
            "📄 CVの分析と評価",
            "💰 給与相談",
            "🏢 会社情報",
            "💡 面接ガイダンス",
            "🎯 キャリア方向性"
        ],

        # Error messages
        "errors": {
            "connection": "サーバーに接続できません。もう一度お試しください。",
            "general": "エラーが発生しました。もう一度お試しください。",
            "file_format": "ファイル形式がサポートされていません。",
            "file_size": "ファイルが大きすぎます。より小さなファイルを選択してください。",
            "upload_failed": "ファイルをアップロードできません。もう一度お試しください。"
        },

        # Success messages
        "success": {
            "cv_uploaded": "CVのアップロードと分析が正常に完了しました！",
            "chat_cleared": "チャット履歴を削除しました",
            "file_processed": "ファイルの処理が正常に完了しました"
        }
    }
}


def get_personality_by_language(language='vi'):
    """Get bot personality configuration by language"""
    return BOT_PERSONALITY_I18N.get(language, BOT_PERSONALITY_I18N['vi'])


def get_greeting_message(language='vi'):
    """Get random greeting message by language"""
    import random
    personality = get_personality_by_language(language)
    return random.choice(personality['greetings'])


def get_welcome_message(language='vi'):
    """Get welcome message by language"""
    personality = get_personality_by_language(language)
    return personality['welcome']


def get_capabilities_list(language='vi'):
    """Get capabilities list by language"""
    personality = get_personality_by_language(language)
    return personality['capabilities']


def get_error_message(error_type, language='vi'):
    """Get error message by type and language"""
    personality = get_personality_by_language(language)
    return personality['errors'].get(error_type, personality['errors']['general'])


def get_success_message(success_type, language='vi'):
    """Get success message by type and language"""
    personality = get_personality_by_language(language)
    return personality['success'].get(success_type, "Success!")

# Language detection helper


# Quick responses by language
QUICK_RESPONSES_I18N = {
    'vi': {
        'greeting': "Xin chào! Tôi có thể giúp gì cho bạn?",
        'thanks': "Cảm ơn bạn! Còn gì khác tôi có thể hỗ trợ không?",
        'goodbye': "Chúc bạn một ngày tốt lành! Hẹn gặp lại!",
        'unclear': "Xin lỗi, tôi chưa hiểu rõ. Bạn có thể nói rõ hơn được không?",
        'off_topic': "Tôi chỉ có thể hỗ trợ các vấn đề liên quan đến việc làm và sự nghiệp. Bạn có câu hỏi nào khác không?"
    },
    'en': {
        'greeting': "Hello! How can I help you?",
        'thanks': "Thank you! Is there anything else I can help with?",
        'goodbye': "Have a great day! See you later!",
        'unclear': "Sorry, I didn't understand. Could you please clarify?",
        'off_topic': "I can only help with job and career-related questions. Do you have any other questions?"
    },
    'ja': {
        'greeting': "こんにちは！何かお手伝いできることはありますか？",
        'thanks': "ありがとうございます！他に何かお手伝いできることはありますか？",
        'goodbye': "良い一日をお過ごしください！また会いましょう！",
        'unclear': "申し訳ございませんが、理解できませんでした。もう少し詳しく説明していただけますか？",
        'off_topic': "私は仕事とキャリアに関する質問のみお手伝いできます。他にご質問はありますか？"
    }
}


def get_quick_response(response_type, language='vi'):
    """Get quick response by type and language"""
    responses = QUICK_RESPONSES_I18N.get(language, QUICK_RESPONSES_I18N['vi'])
    return responses.get(response_type, responses['greeting'])
