#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gemini AI Module cho Email Chatbot
Xử lý tất cả logic liên quan đến Gemini AI
"""

import google.generativeai as genai
import json
from config import GEMINI_API_KEY, AI_SETTINGS, COMPANY_INFO


class GeminiAI:
    """Class xử lý Gemini AI"""

    def __init__(self):
        """Khởi tạo Gemini AI"""
        self.model = None
        self.api_key = GEMINI_API_KEY
        self._configure_api()
        self._initialize_model()

    def _configure_api(self):
        """Cấu hình Gemini API"""
        if not self.api_key:
            print("⚠️ Chưa cấu hình GEMINI_API_KEY")
            return False

        try:
            genai.configure(api_key=self.api_key)
            return True
        except Exception as e:
            print(f"❌ Lỗi cấu hình Gemini API: {str(e)}")
            return False

    def _initialize_model(self):
        """Khởi tạo Gemini model với fallback"""
        if not self.api_key:
            return

        for model_name in AI_SETTINGS['MODEL_NAMES']:
            try:
                self.model = genai.GenerativeModel(model_name)
                print(f"✅ Đã khởi tạo model: {model_name}")
                return
            except Exception as e:
                print(f"⚠️ Không thể khởi tạo model {model_name}: {str(e)}")
                continue

        print("❌ Không thể khởi tạo bất kỳ Gemini model nào")

    def is_available(self):
        """Kiểm tra AI có khả dụng không"""
        return self.model is not None and bool(self.api_key)

    def generate_response(self, email_content, sender_email, subject=""):
        """Tạo phản hồi thông minh sử dụng Gemini AI"""
        if not self.is_available():
            raise Exception("Gemini AI không khả dụng")

        try:
            prompt = self._create_prompt(email_content, sender_email, subject)
            response = self.model.generate_content(prompt)

            if response and response.text:
                return response.text.strip()
            else:
                raise Exception("Không nhận được phản hồi từ Gemini")

        except Exception as e:
            raise Exception(f"Lỗi khi gọi Gemini AI: {str(e)}")

    def generate_structured_content(self, prompt: str, response_format: str = "json"):
        """
        Tạo nội dung với structured output

        Args:
            prompt: Prompt cho AI (đã bao gồm yêu cầu format JSON)
            response_format: Format mong muốn (mặc định: json)

        Returns:
            dict: Parsed response
        """
        if not self.is_available():
            raise Exception("Gemini AI không khả dụng")

        try:
            # Thêm yêu cầu JSON vào prompt nếu chưa có
            if "json" not in prompt.lower():
                prompt += "\n\nVui lòng trả lời theo format JSON hợp lệ."

            # Gọi API thông thường
            response = self.model.generate_content(prompt)

            # Parse JSON response
            if hasattr(response, 'text') and response.text:
                response_text = response.text.strip()

                # Tìm JSON trong response
                start_idx = response_text.find('{')
                end_idx = response_text.rfind('}') + 1

                if start_idx != -1 and end_idx != -1:
                    json_str = response_text[start_idx:end_idx]
                    return json.loads(json_str)
                else:
                    # Thử parse toàn bộ response
                    return json.loads(response_text)
            else:
                raise Exception("Không nhận được phản hồi từ Gemini")

        except json.JSONDecodeError as e:
            raise Exception(f"Lỗi parse JSON: {str(e)}")
        except Exception as e:
            raise Exception(f"Lỗi khi gọi Gemini AI structured: {str(e)}")

    def _create_prompt(self, email_content, sender_email, subject=""):
        """Tạo prompt cho Gemini AI với thông tin công ty đầy đủ"""
        return f"""
Bạn là một AI assistant chuyên nghiệp của {COMPANY_INFO['FULL_NAME']}, một tập đoàn công nghệ ICT hàng đầu với 33 năm kinh nghiệm.

Thông tin email nhận được:
- Người gửi: {sender_email}
- Tiêu đề: {subject}
- Nội dung: {email_content}

Hãy tạo một email phản hồi chuyên nghiệp, thân thiện và hữu ích theo các nguyên tắc sau:

1. Luôn bắt đầu bằng lời chào phù hợp
2. Cảm ơn khách hàng đã liên hệ
3. Phân tích nội dung email và đưa ra phản hồi phù hợp:
   - Nếu là câu hỏi về dịch vụ: giải thích ngắn gọn và hướng dẫn liên hệ
   - Nếu là yêu cầu hỗ trợ: xác nhận đã nhận và cam kết thời gian phản hồi
   - Nếu là khiếu nại: thể hiện sự quan tâm và cam kết giải quyết
   - Nếu là báo giá: hướng dẫn quy trình và thời gian phản hồi
4. Cung cấp thông tin liên hệ bổ sung nếu cần
5. Kết thúc bằng lời chào chuyên nghiệp

Thông tin công ty FOIS ICT PRO:
- Tên: {COMPANY_INFO['NAME']} ({COMPANY_INFO['FULL_NAME']})
- Thành lập: {COMPANY_INFO['ESTABLISHED']} ({COMPANY_INFO['EXPERIENCE_YEARS']} kinh nghiệm)
- Trụ sở: Nhật Bản ({', '.join(COMPANY_INFO['LOCATIONS']['JAPAN'])}) và Việt Nam ({', '.join(COMPANY_INFO['LOCATIONS']['VIETNAM'])})
- Email hỗ trợ: {COMPANY_INFO['EMAIL']}
- Website: {COMPANY_INFO['WEBSITE']}
- Dịch vụ: {', '.join(COMPANY_INFO['SERVICES'])}
- Sứ mệnh: {COMPANY_INFO['MISSION']}
- Tầm nhìn: {COMPANY_INFO['VISION']}
- Giá trị cốt lõi: {', '.join(COMPANY_INFO['CORE_VALUES'])}

Mô tả công ty:
{COMPANY_INFO['DESCRIPTION']}

Viết email bằng tiếng Việt, tối đa 250 từ, tông giọng chuyên nghiệp nhưng thân thiện, thể hiện được giá trị và kinh nghiệm của FOIS ICT PRO.
"""

    def test_connection(self):
        """Test kết nối với Gemini AI"""
        if not self.is_available():
            return False, "Gemini AI không khả dụng"

        try:
            test_response = self.generate_response(
                "Xin chào, đây là test message",
                "<EMAIL>",
                "Test connection"
            )
            return True, f"Kết nối thành công. Sample: {test_response[:50]}..."
        except Exception as e:
            return False, f"Lỗi test: {str(e)}"

    def get_model_info(self):
        """Lấy thông tin model hiện tại"""
        if not self.model:
            return "Không có model nào được khởi tạo"

        return {
            "model_name": getattr(self.model, 'model_name', 'Unknown'),
            "api_key_configured": bool(self.api_key),
            "available": self.is_available()
        }


class AIAssistant:
    """
    Wrapper class để tương thích với code cũ
    Deprecated: Sử dụng GeminiAI thay thế
    """

    def __init__(self):
        """Khởi tạo AI Assistant (deprecated)"""
        print("⚠️ AIAssistant deprecated, sử dụng GeminiAI thay thế")
        self.gemini = GeminiAI()

    def generate_smart_response(self, email_content, sender_email, subject=""):
        """Tạo phản hồi thông minh (deprecated)"""
        return self.gemini.generate_response(email_content, sender_email, subject)

    def _get_fallback_response(self, email_content):
        """Phản hồi dự phòng (deprecated)"""
        return (f"Xin chào,\n\n"
                f"Cảm ơn bạn đã liên hệ với {COMPANY_INFO['NAME']}.\n\n"
                f"Chúng tôi đã nhận được tin nhắn của bạn và sẽ có chuyên viên "
                f"phản hồi trong thời gian sớm nhất có thể.\n\n"
                f"Nếu cần hỗ trợ khẩn cấp, vui lòng liên hệ:\n"
                f"- Email: {COMPANY_INFO['EMAIL']}\n"
                f"- Website: {COMPANY_INFO['WEBSITE']}\n\n"
                f"Trân trọng,\n"
                f"{COMPANY_INFO['NAME']} Support Team")


# Factory function để tạo AI instance
def create_ai_instance():
    """Tạo instance AI"""
    return GeminiAI()


# Singleton instance (optional)
_ai_instance = None


def get_ai_instance():
    """Lấy singleton AI instance"""
    global _ai_instance
    if _ai_instance is None:
        _ai_instance = GeminiAI()
    return _ai_instance
