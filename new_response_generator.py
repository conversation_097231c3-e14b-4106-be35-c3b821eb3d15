#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
New Response Generator for 3-Type Customer Intent System
"""

from typing import Dict, List

from gemini_ai import Gemini<PERSON><PERSON>
from config import COMPANY_INFO, GEMINI_API_KEY

from google import genai
from google.genai import types
from bot_personality import BOT_PERSONALITY
from sample_data import *


class NewResponseGenerator:
    """Response generator cho hệ thống intent mới"""

    def __init__(self, gemini_ai: GeminiAI, email_mode: bool = False):
        self.gemini_ai = gemini_ai
        self.email_mode = email_mode

        self.client = genai.Client(api_key=GEMINI_API_KEY)
        self.gemini_model = "gemini-2.5-flash"

        # Chọn prompt config dựa trên mode
        if email_mode:
            self.prompt_config = self.build_email_system_prompt()
        else:
            self.prompt_config = self.build_system_prompt_from_config(
                BOT_PERSONALITY)

        # Token optimization settings
        self.MAX_HISTORY_TOKENS = 2500  # Approximately 10000 characters
        # 1 token ≈ 4 characters (rough estimate)
        self.TOKEN_ESTIMATION_RATIO = 4
        self.ENABLE_DETAILED_LOGGING = True  # Set to False to reduce console output

    def optimize_conversation_history(self, conversation_history: List, user_input: str) -> List[types.Content]:
        """
        Optimize conversation history to stay within token limits
        Returns list of Content objects in chronological order
        """
        # Always log basic info
        print(f"\n🔍 OPTIMIZING CONVERSATION HISTORY")
        print(
            f"📊 Input: {len(conversation_history) if conversation_history else 0} messages, limit: {self.MAX_HISTORY_TOKENS} tokens")

        if not conversation_history:
            print("❌ No conversation history provided")
            return []

        current_tokens = 0
        history_contents = []
        processed_count = 0
        skipped_count = 0

        print(f"🔄 Processing messages (newest first):")

        # Process messages in reverse order to get most recent first
        for i, msg in enumerate(reversed(conversation_history)):
            processed_count += 1
            msg_index = len(conversation_history) - i  # Original position

            if isinstance(msg, dict):
                msg_type = msg.get('type')
                message_text = msg.get('message', '')

                if msg_type == 'user':
                    role = "user"
                    role_emoji = "👤"
                elif msg_type == 'bot':
                    role = "model"
                    role_emoji = "🤖"
                else:
                    print(
                        f"   ⏭️  #{msg_index}: Skipped system message (type: {msg_type})")
                    skipped_count += 1
                    continue  # Skip system messages

                if not message_text.strip():
                    print(f"   ⏭️  #{msg_index}: Skipped empty message")
                    skipped_count += 1
                    continue

                if message_text.strip() == user_input.strip():
                    print(
                        f"   ⏭️  #{msg_index}: Skipped duplicate of current input")
                    skipped_count += 1
                    continue

            else:
                # Backward compatibility
                message_text = str(msg).strip()
                role = "user"
                role_emoji = "👤"

                if not message_text or message_text == user_input.strip():
                    skipped_count += 1
                    continue

            # Estimate tokens using configured ratio
            token_estimate = len(message_text) // self.TOKEN_ESTIMATION_RATIO
            message_preview = message_text[:50] + \
                "..." if len(message_text) > 50 else message_text

            # Check if adding this message would exceed token limit
            if current_tokens + token_estimate > self.MAX_HISTORY_TOKENS:
                print(
                    f"   🛑 Token limit reached! Current: {current_tokens} + New: {token_estimate} > Limit: {self.MAX_HISTORY_TOKENS}")
                break

            # Add to history (will be reversed later)
            history_contents.append(types.Content(
                role=role,
                parts=[types.Part.from_text(text=message_text)]
            ))

            current_tokens += token_estimate
            print(
                f"   ✅ #{msg_index}: {role_emoji} {role} ({token_estimate} tokens) - \"{message_preview}\"")
            print(
                f"      Running total: {current_tokens}/{self.MAX_HISTORY_TOKENS} tokens")

        # Reverse to maintain chronological order
        optimized_history = list(reversed(history_contents))

        print("\n" + "="*60)
        print("📋 OPTIMIZATION SUMMARY")
        print("="*60)
        print(f"📊 Processing Stats:")
        print(f"   • Messages processed: {processed_count}")
        print(f"   • Messages skipped: {skipped_count}")
        print(f"   • Messages included: {len(optimized_history)}")
        print(
            f"   • Total tokens used: {current_tokens}/{self.MAX_HISTORY_TOKENS}")
        print(
            f"   • Token utilization: {(current_tokens/self.MAX_HISTORY_TOKENS)*100:.1f}%")

        if optimized_history:
            print(f"\n🎯 Final Conversation Order (chronological):")
            for i, content in enumerate(optimized_history, 1):
                role = content.role
                text = content.parts[0].text
                role_emoji = "👤" if role == "user" else "🤖"
                preview = text[:50] + "..." if len(text) > 50 else text
                tokens = len(text) // self.TOKEN_ESTIMATION_RATIO
                print(
                    f"   {i}. {role_emoji} {role}: \"{preview}\" ({tokens} tokens)")

        print("="*60 + "\n")

        return optimized_history

    def configure_token_limits(self, max_history_tokens: int = None, token_ratio: int = None):
        """
        Configure token optimization settings

        Args:
            max_history_tokens: Maximum tokens to use for conversation history
            token_ratio: Character to token ratio (default: 4 chars = 1 token)
        """
        if max_history_tokens is not None:
            self.MAX_HISTORY_TOKENS = max_history_tokens
            print(f"🔧 Updated MAX_HISTORY_TOKENS to {max_history_tokens}")

        if token_ratio is not None:
            self.TOKEN_ESTIMATION_RATIO = token_ratio
            print(f"🔧 Updated TOKEN_ESTIMATION_RATIO to {token_ratio}")

    def get_token_stats(self) -> Dict:
        """Get current token optimization settings"""
        return {
            'max_history_tokens': self.MAX_HISTORY_TOKENS,
            'token_estimation_ratio': self.TOKEN_ESTIMATION_RATIO,
            'estimated_max_characters': self.MAX_HISTORY_TOKENS * self.TOKEN_ESTIMATION_RATIO,
            'detailed_logging_enabled': self.ENABLE_DETAILED_LOGGING
        }

    def set_logging(self, enabled: bool):
        """Enable or disable detailed logging"""
        self.ENABLE_DETAILED_LOGGING = enabled
        print(f"🔧 Detailed logging {'enabled' if enabled else 'disabled'}")

    def build_email_system_prompt(self) -> str:
        """Create system prompt specifically for email mode - more polite and comprehensive"""
        return """
You are Mimi, a professional HR assistant at FOIS ICT PRO on the Asiantech.link platform. You are responding via email, so you need to demonstrate politeness, professionalism, and comprehensive information.

🎯 **EMAIL PERSONALITY TRAITS:**
- More polite and formal than regular chat
- Provide detailed and comprehensive information
- Use professional but friendly language
- Always start and end emails politely
- Clear and logical response structure

🎭 **EMAIL COMMUNICATION STYLE:**
- Tone: Professional but warm
- Formality: Moderately formal
- Emoji: Use sparingly and appropriately
- Response length: Detailed and comprehensive
- Structure: Use headings and bullet points when needed

📧 **EMAIL RULES:**
- Always start with polite greeting
- Thank the sender for contacting
- Answer all questions asked completely
- Provide additional helpful information
- **ALWAYS include the services list when user asks about capabilities or services**
- End with invitation to continue communication
- Include signature and contact information

🌍 **LANGUAGE DETECTION & RESPONSE:**
- Detect user's language from their email content
- If Vietnamese: Respond completely in Vietnamese
- If English: Respond completely in English
- If Japanese: Respond completely in Japanese
- If other languages: Use English as default
- Never mix languages within the same response


🎯 **ALWAYS END WITH THE SERVICES LIST:**
- 🔍 Find job opportunities at FOIS ICT PRO
- 🏢 Information about FOIS ICT PRO company
- 📄 CV submission and job applications

⚠️ **HANDLING OFF-TOPIC SUBJECTS:**
- If question is not related to jobs/career, respond politely
- Explain your scope of support
- Guide user to appropriate support channels
- Maintain professional and friendly tone

📝 **EMAIL RESPONSE STRUCTURE TEMPLATE:**
1. Greeting and thanks
2. Direct answer to question
3. Additional helpful information
4. Follow-up questions (if appropriate)
5. Invitation to continue communication
6. Professional signature

🚫 **IMPORTANT - EMAIL MODE:**
- DO NOT use thinking messages in emails
- ALWAYS respond completely and in detail immediately
- NO need for callbacks or asynchronous processing
- Each email must be a complete and self-sufficient response

Always remember that email is a more formal communication method, requiring professionalism and thoroughness in every response.
        """.strip()

    def build_system_prompt_from_config(self, config) -> str:
        traits = config.get("traits", {})
        style = config.get("communication_style", {})
        addressing = config.get("addressing", {})

        return f"""
    You are a virtual assistant named {config['name']}, working as a {config['role']} at {config['company']}.

Your personality traits include:
- Friendliness: {traits.get('friendliness', 0)}/10
- Professionalism: {traits.get('professionalism', 0)}/10
- Enthusiasm: {traits.get('enthusiasm', 0)}/10
- Helpfulness: {traits.get('helpfulness', 0)}/10
- Patience: {traits.get('patience', 0)}/10
- Humor: {traits.get('humor', 0)}/10
- Empathy: {traits.get('empathy', 0)}/10
- Energy: {traits.get('energy', 0)}/10

Your communication style is:
- Tone: {style.get('tone', 'friendly')}
- Formality: {style.get('formality', 'semi_formal')}
- Emoji usage: {style.get('emoji_usage', 'moderate')}
- Language style: {style.get('language_style', 'conversational')}
- Preferred response length: {style.get('response_length', 'medium')}

When referring to yourself, use: {", ".join(addressing.get("self_pronouns", []))}
When referring to the user, use: {", ".join(addressing.get("user_pronouns", []))}

# Greet only if this is the beginning of the conversation
If this is the user's first message or a new session, greet the user with: "{addressing.get('preferred_greeting', 'Hello')}"

# End only if the user indicates they are done or the conversation is naturally ending
If the user ends the conversation or does not ask follow-up questions, end with: "{addressing.get('preferred_farewell', 'Do you have anything else?')}"

You are a career assistant on the Asiantech.link platform. Your responsibilities are:

- Help users find jobs, improve their CVs, and answer career-related questions.
- Focus on job search, application support, resume guidance, and recruitment-related queries.

Language behavior:
- Always detect the user's input language.
- If the user writes in Vietnamese, respond entirely in Vietnamese (no mixing).
- If the user writes in English, respond entirely in English (no mixing).
- If the user writes in Japanese, respond entirely in Japanese (no mixing).
- Never mix languages in a single sentence or paragraph.
- Maintain consistent tone, tone markers (e.g., emojis), and professional structure in the selected language.

Off-topic policy:
- If the user's message is not related to jobs or careers (e.g., asking about Flutter, programming tutorials, unrelated technical help, or casual conversation), respond politely but briefly, stating that it's outside your support scope.
- Do NOT generate long answers for unrelated topics. Keep responses short and friendly (1–2 sentences max).
- Maintain a consistent tone based on your personality config (friendly, professional, helpful).
        """.strip()

    def generate_response(self, user_id: str, user_input: str, conversation_history: List[str] = None) -> Dict:
        message_description = "Message to response to user"
        if self.email_mode:
            message_description = "Message to send as email response, html format"
        user_intents = [
            "greeting",
            "farewell",
            "ask_company_info",
            "ask_bot_info",
            "search_jobs",
            "filter_jobs",
            "apply_job",
            "cancel_application",
            "upload_resume",
            "update_resume",
            "get_application_status",
            "job_recommendation",
            "follow_up_job",
            "location_query",
            "tech_stack_query",
            "salary_query",
            "interview_process",
            "cv_feedback",
            "smalltalk",
            "off_topic",
            "thank_you",
            "complaint",
            "bug_report",
            "request_human_support",
            "job_it_trending"
        ]
        if self.email_mode:
            user_intents = [
                "farewell",
                "ask_company_info",
                "ask_bot_info",
                "search_jobs",
                "filter_jobs",
                "apply_job",
                "cancel_application",
                "upload_resume",
                "update_resume",
                "off_topic",
                "thank_you",
                "complaint",
                "bug_report",
                "request_human_support",
                "job_it_trending"
            ]
        generate_content_config = types.GenerateContentConfig(
            response_mime_type="application/json",
            temperature=0.5,
            response_schema=types.Schema(
                type=types.Type.OBJECT,
                properties={
                    "thinking_message": types.Schema(
                        type=types.Type.STRING,
                        description="A short message the bot shows to the user while processing a request, e.g., searching for jobs. This message should match the tone and context, and should feel natural to the user."
                    ),
                    "message": types.Schema(type=types.Type.STRING, description=message_description),
                    "response_type": types.Schema(type=types.Type.STRING, description="Type of response (e.g., 'text', 'image', 'video')"),
                    "tone": types.Schema(type=types.Type.STRING, description="Tone of the response (e.g., 'friendly', 'formal')"),
                    "emotion": types.Schema(type=types.Type.STRING, description="Emotion to display (e.g., 'happy', 'sad')"),
                    "user_intent": types.Schema(type=types.Type.STRING, description="Type of user intent",
                                                enum=user_intents
                                                ),
                    "needs_callback": types.Schema(
                        type=types.Type.BOOLEAN,
                        description="Whether this user_intent needs callback for additional processing. In EMAIL MODE, always set to FALSE - provide complete response immediately. Only set to TRUE in chat mode for search_jobs, filter_jobs, salary_query, ask_company_info, cv_feedback, upload_resume, update_resume, job_it_trending"
                    ),
                    "suggestion_answers": types.Schema(type=types.Type.ARRAY, description="List of suggestion answers",
                                                       items=types.Schema(
                                                           type=types.Type.STRING)
                                                       ),
                    "contextual_followup": types.Schema(type=types.Type.OBJECT, description="Contextual follow-up question",
                                                        properties={
                                                            "topic": types.Schema(type=types.Type.STRING, description="Topic of the follow-up question"),
                                                            "prompt": types.Schema(type=types.Type.STRING, description="Prompt for the follow-up question"),
                                                        })

                },
                required=["message", "response_type", "tone", "emotion",
                          "user_intent", "suggestion_answers", "contextual_followup", "thinking_message", "needs_callback"]
            ),
        )

        contents = []

        # Add bot personality prompt - sử dụng email prompt nếu trong email mode
        if self.email_mode:
            prompt_text = f"""
{self.prompt_config}

📧 **EMAIL MODE - COMPLETE JOB SEARCH RESPONSE:**

🌍 **LANGUAGE DETECTION:**
- Detect user's language from their email content
- Respond in the SAME language as the user
- If Vietnamese: Use Vietnamese throughout the entire response
- If English: Use English throughout the entire response
- If other languages: Use English as default

🚫 **ABSOLUTELY FORBIDDEN:**
- Ask for more experience details, salary expectations, or project info
- Request additional information from user
- Write "so I can help you better", "could you please provide", "to assist you further"
- Give generic job requirements without specific positions

✅ **MUST DO IMMEDIATELY:**
1. **Polite greeting** (Dear Sir/Madam... or Kính gửi...)
2. **Thank and acknowledge request**
3. **SPECIFIC JOB LISTINGS** at FOIS ICT PRO:

**🏢 AVAILABLE POSITIONS:**
• Senior Python Developer - Ho Chi Minh City - 25-35 million VND
• Backend Developer - Hanoi - 20-30 million VND
• Full-stack Developer - Remote - 22-32 million VND
• Junior Java Developer - Hanoi - 15-22 million VND
• Frontend Developer - Ho Chi Minh City - 18-28 million VND
• Mobile Developer (Flutter) - Ho Chi Minh City - 20-35 million VND
• DevOps Engineer - Hanoi/Remote - 30-45 million VND
• Data Engineer - Hanoi - 25-40 million VND

4. **Application process: CV → Interview → Offer**
5. **Professional email signature**

🚫 **END EMAIL WITH:**
- "Best regards, Mimi - HR Assistant FOIS ICT PRO" (English)
- "Trân trọng, Mimi - Trợ lý nhân sự FOIS ICT PRO" (Vietnamese)
- "Email: <EMAIL>"
- DO NOT write "if you're interested", "need more info", "don't hesitate"

ALWAYS list at least 3-5 specific positions with salaries, END with signature.

---
Below is the conversation history and the latest message:
"""
        else:
            prompt_text = f"{self.prompt_config}\n\n---\nBelow is the conversation history and the latest message:"

        intro_prompt = types.Part.from_text(text=prompt_text)
        contents.append(types.Content(role="user", parts=[intro_prompt]))

        # Add optimized conversation history with token-based truncation
        optimized_history = self.optimize_conversation_history(
            conversation_history, user_input)
        contents.extend(optimized_history)

        # Add current user message
        contents.append(types.Content(role="user", parts=[
                        types.Part.from_text(text=user_input)]))

        # Generate
        response = self.client.models.generate_content(
            model=self.gemini_model,
            contents=contents,
            config=generate_content_config
        )

        dataJson = response.model_dump().get("parsed")

        # Extract token usage if available - try different approaches
        token_usage = {'input_tokens': 0,
                       'output_tokens': 0, 'total_tokens': 0}

        try:
            # Method 1: Check usage_metadata
            if hasattr(response, 'usage_metadata') and response.usage_metadata:
                usage = response.usage_metadata
                token_usage = {
                    'input_tokens': getattr(usage, 'prompt_token_count', 0),
                    'output_tokens': getattr(usage, 'candidates_token_count', 0),
                    'total_tokens': getattr(usage, 'total_token_count', 0)
                }

            # Method 2: Check if it's in the response data
            elif hasattr(response, 'usage') and response.usage:
                usage = response.usage
                token_usage = {
                    'input_tokens': getattr(usage, 'input_tokens', 0),
                    'output_tokens': getattr(usage, 'output_tokens', 0),
                    'total_tokens': getattr(usage, 'total_tokens', 0)
                }

            # Method 3: Estimate based on text length (fallback)
            else:
                input_text = ' '.join([str(content) for content in contents])
                output_text = str(dataJson.get('message', ''))

                # Rough estimation: ~4 characters per token
                estimated_input = len(input_text) // 4
                estimated_output = len(output_text) // 4

                token_usage = {
                    'input_tokens': estimated_input,
                    'output_tokens': estimated_output,
                    'total_tokens': estimated_input + estimated_output
                }

        except Exception as e:
            print(f"⚠️ Could not extract token usage: {e}")

        dataJson['token_usage'] = token_usage
        return dataJson

    def generate_thinking_message(self, user_id: str, user_input: str, conversation_history: List[str] = None) -> Dict:
        """Generate only thinking message for immediate callback"""
        generate_content_config = types.GenerateContentConfig(
            response_mime_type="application/json",
            temperature=0.5,
            response_schema=types.Schema(
                type=types.Type.OBJECT,
                properties={
                    "thinking_message": types.Schema(
                        type=types.Type.STRING,
                        description="A short message the bot shows to the user while processing a request, e.g., searching for jobs. This message should match the tone and context, and should feel natural to the user."
                    ),
                    "user_intent": types.Schema(type=types.Type.STRING, description="Type of user intent",
                                                enum=[
                                                    "greeting",
                                                    "farewell",
                                                    "ask_company_info",
                                                    "ask_bot_info",
                                                    "search_jobs",
                                                    "filter_jobs",
                                                    "apply_job",
                                                    "cancel_application",
                                                    "upload_resume",
                                                    "update_resume",
                                                    "get_application_status",
                                                    "job_recommendation",
                                                    "follow_up_job",
                                                    "location_query",
                                                    "tech_stack_query",
                                                    "salary_query",
                                                    "interview_process",
                                                    "cv_feedback",
                                                    "smalltalk",
                                                    "off_topic",
                                                    "thank_you",
                                                    "complaint",
                                                    "bug_report",
                                                    "request_human_support"
                                                ],
                                                ),
                    "needs_callback": types.Schema(
                        type=types.Type.BOOLEAN,
                        description="Whether this intent requires a callback for additional processing (e.g., job search, filtering)"
                    )
                },
                required=["thinking_message", "user_intent", "needs_callback"]
            ),
        )

        contents = []

        # Add bot personality prompt for thinking message
        thinking_prompt = types.Part.from_text(
            text=f"{self.prompt_config}\n\n---\nGenerate a short thinking message for this user input. If the intent is search_jobs or filter_jobs, set needs_callback to true and provide an appropriate thinking message like 'Đang tìm kiếm việc làm phù hợp cho bạn...' or 'Đang lọc danh sách công việc...'. For other intents, set needs_callback to false.\n\nUser input: {user_input}"
        )
        contents.append(types.Content(role="user", parts=[thinking_prompt]))

        # Generate
        response = self.client.models.generate_content(
            model=self.gemini_model,
            contents=contents,
            config=generate_content_config
        )

        dataJson = response.model_dump().get("parsed")

        # Extract token usage with fallback estimation
        token_usage = {'input_tokens': 0,
                       'output_tokens': 0, 'total_tokens': 0}

        try:
            if hasattr(response, 'usage_metadata') and response.usage_metadata:
                usage = response.usage_metadata
                token_usage = {
                    'input_tokens': getattr(usage, 'prompt_token_count', 0),
                    'output_tokens': getattr(usage, 'candidates_token_count', 0),
                    'total_tokens': getattr(usage, 'total_token_count', 0)
                }
            else:
                # Fallback estimation
                input_text = str(user_input)
                output_text = str(dataJson.get('thinking_message', ''))

                estimated_input = len(input_text) // 4
                estimated_output = len(output_text) // 4

                token_usage = {
                    'input_tokens': estimated_input,
                    'output_tokens': estimated_output,
                    'total_tokens': estimated_input + estimated_output
                }
        except Exception as e:
            print(f"⚠️ Could not extract token usage: {e}")

        dataJson['token_usage'] = token_usage
        return dataJson

    def generate_thinking_message_response(self, user_id: str, user_input: str, conversation_history: List[str] = None, user_intent: str = None) -> Dict:
        """Generate response after thinking message"""
        generate_content_config = types.GenerateContentConfig(
            response_mime_type="application/json",
            temperature=0.5,
            response_schema=types.Schema(
                type=types.Type.OBJECT,
                properties={
                    "message": types.Schema(type=types.Type.STRING, description="Company info response"),
                    "response_type": types.Schema(type=types.Type.STRING, description="Type of response"),
                    "tone": types.Schema(type=types.Type.STRING, description="Tone of the response"),
                    "emotion": types.Schema(type=types.Type.STRING, description="Emotion to display"),
                    "suggestion_answers": types.Schema(type=types.Type.ARRAY, description="List of suggestion answers",
                                                       items=types.Schema(type=types.Type.STRING)),
                    "contextual_followup": types.Schema(type=types.Type.OBJECT, description="Contextual follow-up question",
                                                        properties={
                                                            "topic": types.Schema(type=types.Type.STRING, description="Topic of the follow-up question"),
                                                            "prompt": types.Schema(type=types.Type.STRING, description="Prompt for the follow-up question"),
                                                        })
                },
                required=["message", "response_type", "tone", "emotion",
                          "suggestion_answers", "contextual_followup"]
            ),
        )
        prompt = self.generate_prompt_based_on_user_intent(
            user_intent, user_input)

        contents = []

        # Add specialized prompt for company info
        company_info_prompt = types.Part.from_text(
            text=prompt)
        contents.append(types.Content(
            role="user", parts=[company_info_prompt]))

        # Add optimized conversation history
        optimized_history = self.optimize_conversation_history(
            conversation_history, user_input)
        contents.extend(optimized_history)

        # Generate
        response = self.client.models.generate_content(
            model=self.gemini_model,
            contents=contents,
            config=generate_content_config
        )

        dataJson = response.model_dump().get("parsed")

        # Extract token usage with fallback estimation
        token_usage = {'input_tokens': 0,
                       'output_tokens': 0, 'total_tokens': 0}

        try:
            if hasattr(response, 'usage_metadata') and response.usage_metadata:
                usage = response.usage_metadata
                token_usage = {
                    'input_tokens': getattr(usage, 'prompt_token_count', 0),
                    'output_tokens': getattr(usage, 'candidates_token_count', 0),
                    'total_tokens': getattr(usage, 'total_token_count', 0)
                }
            else:
                # Fallback estimation
                input_text = str(user_input)
                output_text = str(dataJson.get('message', ''))

                estimated_input = len(input_text) // 4
                estimated_output = len(output_text) // 4

                token_usage = {
                    'input_tokens': estimated_input,
                    'output_tokens': estimated_output,
                    'total_tokens': estimated_input + estimated_output
                }
        except Exception as e:
            print(f"⚠️ Could not extract token usage: {e}")

        dataJson['token_usage'] = token_usage
        return dataJson

    def generate_prompt_based_on_user_intent(self, user_intent, user_input):
        # In email mode, always use email prompt to ensure consistency

        if self.email_mode:
            if user_intent in ['search_jobs', 'filter_jobs', 'salary_query', 'job_recommendation']:
                return f"""
{self.prompt_config}


🚫 **ABSOLUTELY FORBIDDEN:**
- Ask for more experience details, salary expectations, or project info
- Request additional information from user
- Write "so I can help you better" / "để tôi có thể hỗ trợ tốt hơn"
- Write "could you please provide" / "bạn có thể cung cấp"
- Ask about CV, portfolio, or additional information
- End with questions or requests for more info

✅ **MUST DO IMMEDIATELY:**
1. **Polite greeting**
2. **Thank and acknowledge request**
3. **SPECIFIC JOB LISTINGS** at FOIS ICT PRO (mandatory):

For job each position, include:
- Position title
- Work location
- Salary range
- Key requirements
- Brief description


🏢 **AVAILABLE POSITIONS:**

{SAMPLE_JOBS}
Write complete email with specific job listings, end with signature, NO additional questions.

---
User input: {user_input}
"""
            elif user_intent == "ask_company_info":
                return f"""
{self.prompt_config}
---
📧 EMAIL MODE - Complete response about company information:
{COMPANY_DETAILS}
---
User input: {user_input}
"""

        # Chat mode (original logic)
        if user_intent == "ask_company_info":
            return f"""
{self.prompt_config}
---
The user is asking for company info. Here is company details:
{COMPANY_DETAILS}
---
User input: {user_input}
"""
        elif user_intent in ['search_jobs', 'filter_jobs', 'salary_query']:
            return f"""
{self.prompt_config}

---
The user is looking for jobs. Provide a comprehensive job search response with:
1. Available job positions at FOIS ICT PRO
2. Salary ranges
3. Requirements
4. Next steps (CV upload, application process)
5. Relevant suggestions for follow-up questions

Make the response detailed, helpful, and encouraging. Include specific job titles, salary ranges, and actionable advice.
Job available:
{SAMPLE_JOBS}

Salary ranges:
{SALARY_RANGES}

---
User input: {user_input}
"""
        elif user_intent in ["cv_feedback", "upload_resume", "update_resume"]:
            return f"""
{self.prompt_config}

🎯 Your core responsibilities include:
1. Reviewing user CVs and giving feedback.
2. Suggesting improvements to CVs for better job matching.
       
Here is CV and user input:
{user_input}
"""
        elif user_intent == "job_it_trending":
            # Prepare market data context for AI
            market_context = f"""
    📊 **VIETNAM IT MARKET DATA 2024-2025** (From ITviec, TopDev, Navigos Reports):

    🔥 **HOT TECHNOLOGIES & SALARY RANGES:**
    • AI/ML: {HOT_TECH_STACK_2024['ai_ml']['salary_range']} - {HOT_TECH_STACK_2024['ai_ml']['note']}
    • Python: {HOT_TECH_STACK_2024['backend']['salary_range']} - {HOT_TECH_STACK_2024['backend']['note']}
    • Cloud/DevOps: {HOT_TECH_STACK_2024['cloud_devops']['salary_range']} - {HOT_TECH_STACK_2024['cloud_devops']['note']}
    • Blockchain: {HOT_TECH_STACK_2024['blockchain']['salary_range']} - {HOT_TECH_STACK_2024['blockchain']['note']}

    💰 **TOP PAYING POSITIONS:**
    • AI/ML Engineer: {SALARY_RANGES['ai_ml_engineer']['range']} - {SALARY_RANGES['ai_ml_engineer']['market_trend']}
    • Python Developer: {SALARY_RANGES['python_developer']['range']} - {SALARY_RANGES['python_developer']['market_trend']}
    • Blockchain Engineer: {SALARY_RANGES['blockchain_engineer']['range']} - {SALARY_RANGES['blockchain_engineer']['market_trend']}

    🚀 **MARKET TRENDS:**
    • Status: {IT_MARKET_TRENDS_2024_2025['overview']['status']}
    • Key Drivers: {', '.join(IT_MARKET_TRENDS_2024_2025['overview']['key_drivers'])}
    • Challenges: {', '.join(IT_MARKET_TRENDS_2024_2025['overview']['challenges'])}

    💼 **FREELANCE MARKET:**
    • AI/ML Projects: {FREELANCE_MARKET_2024['top_earning_categories']['ai_ml_development']['avg_monthly']} (highest)
    • Web Development: {FREELANCE_MARKET_2024['top_earning_categories']['web_development']['avg_monthly']}
    • Mobile Development: {FREELANCE_MARKET_2024['top_earning_categories']['mobile_development']['avg_monthly']}

    🎯 **MOST DEMANDED POSITIONS 2024:**
    {chr(10).join([f"• {pos['title']}: {pos['avg_salary']} - {pos['market_note']}" for pos in JOB_MARKET_INSIGHTS_2024['most_demanded_positions'][:3]])}

    ⚠️ **HIRING CHALLENGES:**
    {chr(10).join([f"• {challenge}" for challenge in JOB_MARKET_INSIGHTS_2024['hiring_challenges'][:3]])}

    🔮 **2025 FORECAST:**
    • Growth Sectors: {', '.join(IT_FORECAST_2025['growth_sectors'])}
    • Market Outlook: {IT_FORECAST_2025['market_outlook']}

    🔥 **HOT JOB OPPORTUNITIES AT FOIS:**
    {chr(10).join([f"• {job['title']} {job.get('hot_tag', '')}: {job['salary_range']} - {job['location']}" for job in SAMPLE_JOBS[:2]])}
    """
            return f"""
{self.prompt_config}

🎯 **SPECIALIZED ROLE: IT Market Analyst & Career Advisor**

You are now an expert IT market analyst with access to the latest Vietnam IT market data from 2024-2025 reports (ITviec, TopDev, Navigos). Your mission is to provide accurate, data-driven insights about:

1. 🔥 Hot technology trends and salary ranges
2. 📈 Market demand and growth sectors
3. 💰 Salary insights and career opportunities
4. 🚀 Future predictions and recommendations

**CURRENT MARKET DATA:**
{market_context}

**USER QUESTION:** {user_input}

**INSTRUCTIONS:**
- Use REAL DATA from the market reports above
- Provide specific salary ranges with VNĐ amounts
- Mention trending technologies and their demand levels
- Include actionable career advice
- Be enthusiastic about opportunities but realistic about challenges
- Use emojis and formatting to make data engaging
- Always cite that data comes from "latest 2024-2025 market reports"

**RESPONSE STYLE:**
- Professional yet friendly and encouraging
- Data-driven with specific numbers
- Include both opportunities and realistic expectations
- Suggest next steps for career development
"""

    def get_market_insights_summary(self) -> Dict:
        """Get a quick summary of current IT market insights"""
        from sample_data import (
            IT_MARKET_TRENDS_2024_2025, HOT_TECH_STACK_2024,
            JOB_MARKET_INSIGHTS_2024
        )

        return {
            "market_status": IT_MARKET_TRENDS_2024_2025['overview']['status'],
            "hot_technologies": {
                "ai_ml": {
                    "salary": HOT_TECH_STACK_2024['ai_ml']['salary_range'],
                    "demand": HOT_TECH_STACK_2024['ai_ml']['demand']
                },
                "python": {
                    "salary": HOT_TECH_STACK_2024['backend']['salary_range'],
                    "demand": HOT_TECH_STACK_2024['backend']['demand']
                },
                "cloud": {
                    "salary": HOT_TECH_STACK_2024['cloud_devops']['salary_range'],
                    "demand": HOT_TECH_STACK_2024['cloud_devops']['demand']
                }
            },
            "top_positions": [pos["title"] for pos in JOB_MARKET_INSIGHTS_2024["most_demanded_positions"][:3]],
            "salary_growth": "15-25% khi chuyển việc",
            "key_trends": [
                "AI/ML dẫn đầu về lương và nhu cầu",
                "Python là ngôn ngữ hot nhất",
                "Remote work trở thành chuẩn mực",
                "Thiếu hụt nhân lực có kinh nghiệm"
            ]
        }
