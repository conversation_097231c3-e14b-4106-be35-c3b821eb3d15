<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ company_info.FULL_NAME }} - AI Chatbot</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="chat-container">
        <!-- Header -->
        <div class="chat-header">
            <div class="header-content">
                <div class="company-info">
                    <div class="company-logo">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="company-details">
                        <h1>{{ company_info.NAME }}</h1>
                        <p>AI Assistant - {{ company_info.EXPERIENCE_YEARS }} kinh nghiệm</p>
                    </div>
                </div>
                <div class="header-actions">
                    <button id="clearChat" class="btn-icon" title="Xóa lịch sử chat">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button id="toggleInfo" class="btn-icon" title="Thông tin công ty">
                        <i class="fas fa-info-circle"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Company Info Panel -->
        <div id="companyInfoPanel" class="company-panel hidden">
            <div class="panel-content">
                <h3><i class="fas fa-building"></i> {{ company_info.FULL_NAME }}</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <i class="fas fa-calendar"></i>
                        <span>Thành lập: {{ company_info.ESTABLISHED }}</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>Trụ sở: Nhật Bản & Việt Nam</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-bullseye"></i>
                        <span>{{ company_info.MISSION }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Messages -->
        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                <div class="bot-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <h3>👋 Xin chào! Tôi là AI Assistant của {{ company_info.NAME }}</h3>
                    <p>Tôi có thể giúp bạn:</p>
                    <ul>
                        <li>🔍 Tìm kiếm cơ hội việc làm</li>
                        <li>🏢 Thông tin về công ty</li>
                        <li>💰 Mức lương theo vị trí</li>
                        <li>📚 Hướng dẫn sử dụng platform</li>
                        <li>📄 Hỗ trợ ứng tuyển</li>
                    </ul>
                    <p>Bạn muốn hỏi gì hôm nay? 😊</p>
                </div>
            </div>
        </div>

        <!-- Suggestions -->
        <div class="suggestions" id="suggestions">
            <div class="suggestions-title">💡 Gợi ý câu hỏi:</div>
            <div class="suggestions-list" id="suggestionsList">
                <!-- Suggestions will be loaded here -->
            </div>
        </div>

        <!-- Chat Input -->
        <div class="chat-input">
            <div class="input-container">
                <input 
                    type="text" 
                    id="messageInput" 
                    placeholder="Nhập câu hỏi của bạn..." 
                    autocomplete="off"
                >
                <button id="sendButton" class="send-button">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
            <div class="input-footer">
                <span class="powered-by">
                    Powered by <strong>{{ company_info.NAME }}</strong> AI
                </span>
            </div>
        </div>

        <!-- Typing Indicator -->
        <div class="typing-indicator hidden" id="typingIndicator">
            <div class="bot-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay hidden" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-robot fa-spin"></i>
            <p>Đang khởi tạo AI Assistant...</p>
        </div>
    </div>

    <!-- Emergency Loading Fix -->
    <script>
        // Force hide loading overlay immediately
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚨 Emergency loading fix activated');

            // Hide all possible loading elements
            const selectors = [
                '#loadingOverlay',
                '.loading-overlay',
                '.loading',
                '[class*="loading"]',
                '[id*="loading"]'
            ];

            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    el.style.display = 'none';
                    el.classList.add('hidden');
                    console.log(`🔄 Hidden element: ${selector}`);
                });
            });

            // Ensure body is visible
            document.body.style.visibility = 'visible';
            document.body.style.opacity = '1';

            console.log('✅ Emergency loading fix completed');
        });

        // Also try immediately if DOM is already loaded
        if (document.readyState !== 'loading') {
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
                console.log('🔄 Immediate loading fix applied');
            }
        }
    </script>

    <!-- Scripts -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
