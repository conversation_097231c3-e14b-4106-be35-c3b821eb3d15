 <!DOCTYPE html>
<html lang="vi" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ company_info.FULL_NAME }} - AI Assistant</title>

    <!-- Force dark mode immediately before any CSS loads -->
    <script>
        (function() {
            console.log('🌙 Pre-CSS dark mode force...');
            document.documentElement.setAttribute('data-theme', 'dark');
            document.documentElement.style.backgroundColor = '#0f222a';
            document.documentElement.style.color = '#f1f5f9';
            document.body.style.backgroundColor = '#0f222a';
            document.body.style.color = '#f1f5f9';

            if (typeof localStorage !== 'undefined') {
                localStorage.setItem('chatbot-theme', 'dark');
            }

            // Force dark mode on any dynamically created elements
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            node.style.backgroundColor = 'inherit';
                            node.style.color = '#f1f5f9';
                        }
                    });
                });
            });

            // Start observing when DOM is ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', function() {
                    observer.observe(document.body, { childList: true, subtree: true });
                });
            } else {
                observer.observe(document.body, { childList: true, subtree: true });
            }
        })();
    </script>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/modern.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/i18n.css') }}">

    <!-- Force Dark Mode Immediately -->
    <style>
        /* Force dark mode variables immediately */
        :root {
            --bg-primary: #0f222a !important;
            --bg-secondary: #1e293b !important;
            --bg-tertiary: #334155 !important;
            --text-primary: #f1f5f9 !important;
            --text-secondary: #cbd5e1 !important;
            --text-muted: #94a3b8 !important;
            --border-color: #334155 !important;
            --message-bg: #1e293b !important;
            --sidebar-bg: #0f222a !important;
            --input-bg: #334155 !important;

            /* Force all white/light variables to dark */
            --white: #1e293b !important;
            --gray-50: #334155 !important;
            --gray-100: #475569 !important;
            --gray-200: #64748b !important;
            --surface-color: #1e293b !important;
            --hover-color: #334155 !important;
        }

        /* Override any light mode styles immediately */
        html, body {
            background-color: #0f222a !important;
            color: #f1f5f9 !important;
        }

        /* Force body gradient */
        body {
            background: linear-gradient(135deg, #0f222a 0%, #1e293b 100%) !important;
        }

        /* Force all major containers */
        .app-container {
            background-color: #0f222a !important;
        }

        .sidebar {
            background-color: #0f222a !important;
            border-right: 1px solid #334155 !important;
        }

        .chat-container {
            background-color: #0f222a !important;
        }

        .background {
            background-color: #0f222a !important;
        }

        .bg-gradient {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%) !important;
        }

        /* Force background patterns */
        .bg-pattern {
            background-image:
                radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%) !important;
            background-size: 100px 100px !important;
        }

        /* Force all container backgrounds */
        * {
            scrollbar-color: #475569 #1e293b !important;
        }

        /* Force webkit scrollbars */
        ::-webkit-scrollbar-track {
            background: #1e293b !important;
        }

        ::-webkit-scrollbar-thumb {
            background: #475569 !important;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #64748b !important;
        }

        /* Force message styles */
        .message {
            background-color: #1e293b !important;
            color: #f1f5f9 !important;
        }

        .message.user {
            background-color: #334155 !important;
        }

        /* Force input styles */
        .message-input {
            background-color: #334155 !important;
            color: #f1f5f9 !important;
            border: 1px solid #475569 !important;
        }

        /* Force button styles */
        .btn-primary {
            background-color: #3b82f6 !important;
            color: #ffffff !important;
        }

        /* Force all white backgrounds to dark with proper text colors */
        .company-card, .chat-header, .message-bubble, .welcome-message .message-bubble,
        .quick-actions, .suggestions-container, .input-container, .typing-bubble,
        .modal-content, .modal-footer, .job-match, .token-usage-container {
            background-color: #1e293b !important;
            background: #1e293b !important;
            color: #f1f5f9 !important;
        }

        /* Force all gray-50 backgrounds to dark with proper text colors */
        .input-wrapper, .messages-container, .typing-indicator, .upload-area,
        .analysis-result, .token-stat {
            background-color: #334155 !important;
            background: #334155 !important;
            color: #f1f5f9 !important;
        }

        /* Force gradients to dark */
        .messages-container, .typing-indicator {
            background: linear-gradient(180deg, #1e293b 0%, #0f222a 100%) !important;
        }

        /* Force welcome message */
        .welcome-message .message-bubble {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%) !important;
            border-color: #3b82f6 !important;
            color: #f1f5f9 !important;
        }

        /* Force all borders to dark */
        .sidebar, .chat-header, .input-container, .message-bubble, .suggestion-item {
            border-color: #475569 !important;
        }

        /* Nuclear option: Force any remaining white/light backgrounds */
        [style*="background-color: #ffffff"], [style*="background-color: white"],
        [style*="background-color: #f8fafc"], [style*="background-color: #f1f5f9"],
        [style*="background: #ffffff"], [style*="background: white"],
        [style*="background: #f8fafc"], [style*="background: #f1f5f9"] {
            background-color: #1e293b !important;
            background: #1e293b !important;
            color: #f1f5f9 !important;
        }

        /* Force text colors for all elements */
        *, *::before, *::after {
            color: #f1f5f9 !important;
        }

        /* Force specific text elements */
        h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, textarea, label {
            color: #f1f5f9 !important;
        }

        /* Force sidebar text */
        .sidebar *, .company-card *, .detail-item *, .action-item * {
            color: #f1f5f9 !important;
        }

        /* Force chat text */
        .message *, .message-bubble *, .welcome-message *, .suggestion-item * {
            color: #f1f5f9 !important;
        }

        /* Force input text */
        .message-input, .message-input::placeholder {
            color: #f1f5f9 !important;
        }

        /* Exception: Keep user message text white */
        .user-message .message-bubble * {
            color: #ffffff !important;
        }

        /* Exception: Keep button text appropriate */
        .btn-primary, .btn-primary * {
            color: #ffffff !important;
        }

        /* COMPREHENSIVE BACKGROUND SYNC - Force ALL possible white backgrounds */

        /* Main containers */
        .app-container, .sidebar, .chat-container, .main-content {
            background: #0f222a !important;
            background-color: #0f222a !important;
        }

        /* Background layers */
        .background, .bg-gradient, .bg-pattern {
            background: linear-gradient(135deg, #0f222a 0%, #1e293b 100%) !important;
            background-color: #0f222a !important;
        }

        /* All cards and panels */
        .company-card, .feature-card, .info-card, .stats-card {
            background: #1e293b !important;
            background-color: #1e293b !important;
        }

        /* All message related */
        .messages-container, .message, .message-bubble, .bot-message, .user-message {
            background: #1e293b !important;
            background-color: #1e293b !important;
        }

        /* Input and form elements */
        .input-container, .input-wrapper, .message-input, .form-group {
            background: #334155 !important;
            background-color: #334155 !important;
        }

        /* Headers and footers */
        .header, .footer, .chat-header, .sidebar-header {
            background: #1e293b !important;
            background-color: #1e293b !important;
        }

        /* Modals and overlays */
        .modal, .modal-content, .modal-header, .modal-body, .modal-footer {
            background: #1e293b !important;
            background-color: #1e293b !important;
        }

        /* Buttons and interactive elements */
        .btn, .button, .btn-secondary, .btn-icon {
            background: #334155 !important;
            background-color: #334155 !important;
        }

        /* Lists and items */
        .list, .list-item, .suggestion-item, .action-item, .detail-item {
            background: #334155 !important;
            background-color: #334155 !important;
        }

        /* Dropdowns and selectors */
        .dropdown, .dropdown-menu, .select, .option {
            background: #334155 !important;
            background-color: #334155 !important;
        }

        /* FORCE ALL PSEUDO-ELEMENTS AND STATES */

        /* Hover states */
        *:hover, .btn:hover, .button:hover, .suggestion-item:hover, .action-item:hover {
            background-color: #475569 !important;
        }

        /* Focus states */
        *:focus, input:focus, textarea:focus, .message-input:focus {
            background-color: #334155 !important;
            outline-color: #3b82f6 !important;
        }

        /* Before and after pseudo-elements */
        *::before, *::after {
            background-color: transparent !important;
        }

        /* NUCLEAR OPTION: Override any inline styles */
        [style] {
            background-color: #1e293b !important;
            background: #1e293b !important;
        }

        /* Override any CSS that might set white backgrounds */
        [class*="white"], [class*="light"], [class*="bg-white"], [class*="bg-light"] {
            background-color: #1e293b !important;
            background: #1e293b !important;
        }

        /* Force specific problematic elements */
        body > *, html > *, #root > *, .app > * {
            background-color: inherit !important;
        }

        /* Ensure consistent dark theme */
        html[data-theme="light"] {
            background-color: #0f222a !important;
        }

        html[data-theme="light"] * {
            background-color: inherit !important;
            color: #f1f5f9 !important;
        }
    </style>
</head>
<body>
    <!-- Background -->
    <div class="background">
        <div class="bg-gradient"></div>
        <div class="bg-pattern"></div>
    </div>

    <!-- Main Container -->
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <div class="logo-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="logo-text">
                        <h2>FOIS AI</h2>
                        <span>Assistant</span>
                    </div>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="sidebar-content">
                <!-- Company Info -->
                <div class="company-card">
                    <div class="company-header">
                        <i class="fas fa-building"></i>
                        <h3>{{ company_info.NAME }}</h3>
                    </div>
                    <div class="company-details">
                        <div class="detail-item">
                            <i class="fas fa-calendar"></i>
                            <span>{{ company_info.EXPERIENCE_YEARS }} <span data-i18n="sidebar.company_experience">kinh nghiệm</span></span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span data-i18n="sidebar.location">Nhật Bản & Việt Nam</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-bullseye"></i>
                            <span data-i18n="sidebar.mission">Tạo ra giải pháp công nghệ tối ưu cho doanh nghiệp...</span>
                        </div>
                    </div>
                </div>

              
                <!-- Settings -->
                <div class="sidebar-footer">
                    <!-- Language selector will be inserted here by JavaScript -->
                    <button type="button" class="btn-secondary" id="clearChat" data-i18n-title="button.clear_chat" title="Xóa lịch sử chat">
                        <i class="fas fa-trash"></i>
                        <span data-i18n="sidebar.clear_history">Xóa lịch sử</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Chat Area -->
        <div class="chat-container">
            <!-- Header -->
            <div class="chat-header">
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="header-info">
                    <div class="avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="info">
                        <h3 data-i18n="header.title">FOIS ICT PRO AI Assistant</h3>
                        <span class="status online">
                            <i class="fas fa-circle"></i>
                            <span data-i18n="header.status">Đang hoạt động</span>
                        </span>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="btn-icon" id="tokenToggle" title="Hiển thị/Ẩn thông tin Token">
                        <i class="fas fa-chart-bar"></i>
                    </button>
                   
                </div>
            </div>

            <!-- Messages Area -->
            <div class="messages-container" id="messagesContainer">
                <!-- Welcome Message -->
                <div class="message-group">
                    <div class="message bot-message welcome-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-bubble">
                                <div class="welcome-header">
                                    <h3 data-i18n="message.welcome">👋 Xin chào! Tôi là AI Assistant của FOIS ICT PRO</h3>
                                </div>
                                <div class="welcome-features">
                                    <div class="feature-grid">
                                        <div class="feature-item" data-action="jobs">
                                            <i class="fas fa-search"></i>
                                            <span data-i18n="actions.jobs">Tìm kiếm việc làm</span>
                                        </div>
                                        <div class="feature-item" data-action="company">
                                            <i class="fas fa-building"></i>
                                            <span data-i18n="actions.company">Thông tin công ty</span>
                                        </div>
                                        <div class="feature-item" data-action="salary">
                                            <i class="fas fa-money-bill"></i>
                                            <span data-i18n="actions.salary">Mức lương</span>
                                        </div>
                                        <div class="feature-item" data-action="guide">
                                            <i class="fas fa-graduation-cap"></i>
                                            <span data-i18n="actions.guide">Hướng dẫn</span>
                                        </div>
                                    </div>
                                </div>
                                <p class="welcome-text" data-i18n="welcome.start_message">Hãy bắt đầu bằng cách hỏi tôi bất cứ điều gì! 😊</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Suggestions -->
            <div class="suggestions-container" id="suggestionsContainer">
                <div class="suggestions-header">
                    <i class="fas fa-lightbulb"></i>
                    <span data-i18n="chat.suggestions_title">Gợi ý câu hỏi</span>
                </div>
                <div class="suggestions-list" id="suggestionsList">
                    <!-- Suggestions will be loaded here -->
                </div>
            </div>

            <!-- Typing Indicator -->
            <div class="typing-indicator hidden" id="typingIndicator">
                <div class="message bot-message">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <div class="typing-bubble">
                            <div class="typing-dots">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Input Area -->
            <div class="input-container">
                <div class="input-wrapper">
                    <div class="input-field">
                        <textarea
                            id="messageInput"
                      
                            placeholder=""
                            rows="1"
                        ></textarea>
                        <div class="input-actions">
                            <button class="btn-attach" data-i18n-title="button.attach" title="Đính kèm file" id="attachFileBtn">
                                <i class="fas fa-paperclip"></i>
                            </button>
                            <button class="btn-send" data-i18n-title="button.send" title="Gửi tin nhắn" id="sendButton">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="input-footer">
                    <span class="powered-by" data-i18n="footer.powered_by">
                        Powered by <strong>FOIS ICT PRO</strong> AI Technology
                    </span>
                </div>

                <!-- Token Usage Display -->
                <div class="token-usage-container hidden" id="tokenUsageContainer">
                    <div class="token-usage-header">
                        <i class="fas fa-chart-bar"></i>
                        <span>Token Usage Statistics</span>
                        <button class="btn-icon-small" id="resetTokens" title="Reset token counter">
                            <i class="fas fa-redo"></i>
                        </button>
                    </div>
                    <div class="token-usage-stats">
                        <div class="token-stat">
                            <div class="token-label">Total Tokens</div>
                            <div class="token-value" id="totalTokens">0</div>
                        </div>
                        <div class="token-stat">
                            <div class="token-label">Input Tokens</div>
                            <div class="token-value" id="inputTokens">0</div>
                        </div>
                        <div class="token-stat">
                            <div class="token-label">Output Tokens</div>
                            <div class="token-value" id="outputTokens">0</div>
                        </div>
                        <div class="token-stat">
                            <div class="token-label">Messages</div>
                            <div class="token-value" id="messageCount">0</div>
                        </div>
                    </div>
                    <div class="token-usage-details">
                        <div class="last-message-tokens">
                            <span class="token-detail-label">Last Message:</span>
                            <span class="token-detail-value" id="lastMessageTokens">0 tokens</span>
                        </div>
                        <div class="avg-tokens">
                            <span class="token-detail-label">Avg per Message:</span>
                            <span class="token-detail-value" id="avgTokensPerMessage">0 tokens</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CV Upload Modal -->
    <div class="modal hidden" id="cvUploadModal">
        <div class="modal-overlay" id="modalOverlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-file-upload"></i> Upload CV để AI phân tích</h3>
                <button class="modal-close" id="modalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <div class="upload-text">
                        <h4>Kéo thả file CV hoặc click để chọn</h4>
                        <p>Hỗ trợ: PDF, DOCX, TXT, JPG, PNG (tối đa 10MB)</p>
                    </div>
                    <input type="file" id="cvFileInput" accept=".pdf,.docx,.txt,.jpg,.jpeg,.png" hidden>
                    <button class="btn-upload" id="selectFileBtn">
                        <i class="fas fa-folder-open"></i>
                        Chọn file CV
                    </button>
                </div>

                <div class="upload-progress hidden" id="uploadProgress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">Đang upload...</div>
                </div>

                <div class="upload-result hidden" id="uploadResult">
                    <!-- Kết quả phân tích sẽ hiển thị ở đây -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" id="cancelUpload">Hủy</button>
                <button class="btn-primary hidden" id="uploadBtn">
                    <i class="fas fa-upload"></i>
                    Upload & Phân tích
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Pass initial language and theme from server to client
        window.INITIAL_LANGUAGE = '{{ initial_language }}';
        window.INITIAL_THEME = '{{ initial_theme }}';

        // Force dark theme immediately
        console.log('🌙 Setting initial theme:', window.INITIAL_THEME);
        document.documentElement.setAttribute('data-theme', window.INITIAL_THEME || 'dark');
    </script>
    <script src="{{ url_for('static', filename='js/i18n.js') }}"></script>
    <script src="{{ url_for('static', filename='js/modern.js') }}"></script>
</body>
</html>
