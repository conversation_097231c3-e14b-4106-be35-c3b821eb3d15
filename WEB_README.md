# 🌐 FOIS Chatbot Web Interface

Giao diện web hiện đại cho EmailChatbot của FOIS ICT PRO với AI-powered conversation.

## ✨ Features

### 🎯 **Core Features**
- **Real-time Chat**: <PERSON><PERSON> trự<PERSON> tiếp với AI Assistant
- **Smart Intent Detection**: Nhận diện ý định với 11 loại intent
- **Rich Responses**: Phản hồi phong phú với formatting và emoji
- **Session Management**: L<PERSON>u lịch sử conversation theo session
- **Responsive Design**: Tương thích mọi thiết bị

### 🎨 **UI/UX Features**
- **Modern Design**: Giao diện hiện đại với gradient và animations
- **Typing Indicator**: Hiệu ứng typing khi AI đang suy nghĩ
- **Smart Suggestions**: Gợi ý câu hỏi thông minh
- **Company Info Panel**: Panel thông tin công ty có thể toggle
- **Message Formatting**: Hỗ trợ markdown-like formatting
- **Notifications**: <PERSON>h<PERSON>ng báo lỗi và thành công

### 🤖 **AI Features**
- **11 Intent Types**: Hỗ trợ đầy đủ 11 loại intent
- **Context Awareness**: AI hiểu context cuộc hội thoại
- **Entity Extraction**: Trích xuất thông tin từ câu hỏi
- **Structured Output**: Response có cấu trúc rõ ràng
- **Sample Data**: Dữ liệu mẫu phong phú cho demo

## 🚀 Quick Start

### 1. **Cài đặt Dependencies**
```bash
pip install flask flask-cors requests
```

### 2. **Cấu hình Environment**
Đảm bảo file `.env` có GEMINI_API_KEY:
```env
GEMINI_API_KEY=your_api_key_here
```

### 3. **Chạy Web Server**
```bash
python run_web.py
```

### 4. **Truy cập Chatbot**
Mở browser và truy cập: http://localhost:5000

## 📁 Project Structure

```
├── web_app.py              # Flask application
├── run_web.py              # Script chạy web server
├── templates/
│   └── index.html          # HTML template
├── static/
│   ├── css/
│   │   └── style.css       # CSS styling
│   └── js/
│       └── app.js          # JavaScript logic
└── demo_web_interface.py   # Demo script
```

## 🔌 API Endpoints

### **Chat API**
```http
POST /api/chat
Content-Type: application/json

{
    "message": "Tôi muốn tìm job Python Developer"
}
```

### **Suggestions API**
```http
GET /api/suggestions
```

### **Company Info API**
```http
GET /api/company-info
```

### **Conversation API**
```http
GET /api/conversation
```

### **Clear Chat API**
```http
POST /api/clear
```

### **Health Check**
```http
GET /health
```

## 🎯 Supported Intents

| Intent | Mô tả | Ví dụ |
|--------|-------|-------|
| **GREETINGS** | Chào hỏi | "Xin chào!" |
| **ASK_COMPANY_INFO** | Hỏi về công ty | "FOIS ICT PRO là gì?" |
| **ASK_PLATFORM_USAGE** | Hướng dẫn sử dụng | "Cách sử dụng hệ thống?" |
| **ASK_JOB_OPPORTUNITIES** | Tìm việc làm | "Tìm job Python Developer" |
| **ASK_JOB_DETAILS** | Chi tiết job | "Chi tiết job này" |
| **SHARE_PROFILE** | Gửi CV | "Tôi muốn gửi CV" |
| **NOT_INTERESTED** | Từ chối | "Không quan tâm" |
| **SALARY_EXPECTATION** | Hỏi lương | "Mức lương bao nhiêu?" |
| **FEEDBACK_ON_JOB_MATCH** | Phản hồi job | "Job không phù hợp" |
| **FOLLOW_UP** | Tiếp tục hội thoại | "Vậy tiếp theo sao?" |
| **OTHER** | Ngoài phạm vi | "Thời tiết hôm nay" |

## 🎨 Customization

### **Thay đổi Theme**
Sửa file `static/css/style.css`:
```css
/* Thay đổi màu chủ đạo */
:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --accent-color: #e74c3c;
}
```

### **Thêm Features**
Sửa file `static/js/app.js`:
```javascript
// Thêm feature mới
class ChatBot {
    // Your custom features here
}
```

### **Thêm API Endpoints**
Sửa file `web_app.py`:
```python
@app.route('/api/your-endpoint', methods=['POST'])
def your_endpoint():
    # Your API logic here
    return jsonify({'result': 'success'})
```

## 🧪 Testing

### **Test API Endpoints**
```bash
python demo_web_interface.py
```

### **Manual Testing**
1. Mở http://localhost:5000
2. Test các loại câu hỏi khác nhau
3. Kiểm tra responsive design
4. Test error handling

## 🔧 Development

### **Debug Mode**
Web server chạy ở debug mode mặc định:
- Auto-reload khi code thay đổi
- Detailed error messages
- Debug toolbar

### **Production Deployment**
Để deploy production, sử dụng WSGI server:
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 web_app:app
```

## 📊 Performance

### **Response Times**
- Health Check: ~50ms
- Chat API: ~2-5s (tùy thuộc AI)
- Static Assets: ~10ms
- Suggestions: ~20ms

### **Concurrent Users**
- Development: ~10 users
- Production: ~100+ users (với proper WSGI server)

## 🛠️ Troubleshooting

### **Common Issues**

1. **Server không start**
   ```bash
   # Check dependencies
   pip install -r requirements.txt
   
   # Check .env file
   cat .env | grep GEMINI_API_KEY
   ```

2. **AI không phản hồi**
   ```bash
   # Test Gemini AI
   python -c "from gemini_ai import GeminiAI; print(GeminiAI().is_available())"
   ```

3. **CSS/JS không load**
   ```bash
   # Check static files
   ls -la static/css/style.css
   ls -la static/js/app.js
   ```

## 🎉 Demo

Xem demo tại: http://localhost:5000

**Sample Conversations:**
- "Xin chào! Tôi muốn tìm hiểu về FOIS ICT PRO"
- "Tôi muốn tìm job Python Developer"
- "Mức lương AI Engineer bao nhiêu?"
- "Hướng dẫn sử dụng hệ thống"

## 📞 Support

Nếu gặp vấn đề, vui lòng:
1. Check logs trong terminal
2. Test API endpoints với demo script
3. Kiểm tra browser console cho JavaScript errors

---

**Powered by FOIS ICT PRO - 33 năm kinh nghiệm ICT** 🚀
