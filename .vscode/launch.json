{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "🚀 Launch FOIS Chatbot (Port 5001)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/web_app.py",
            "console": "integratedTerminal",
            "env": {
                "FLASK_ENV": "development",
                "FLASK_DEBUG": "1"
            },
            "args": ["--host=0.0.0.0", "--port=5001"],
            "cwd": "${workspaceFolder}",
            "stopOnEntry": false,
            "autoReload": {
                "enable": true
            }
        },
        {
            "name": "🌐 Debug Frontend (Chrome)",
            "type": "chrome",
            "request": "launch",
            "url": "http://localhost:5001/modern",
            "webRoot": "${workspaceFolder}/static",
            "sourceMaps": true,
            "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile",
            "runtimeArgs": [
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor"
            ]
        },
        {
            "name": "🔗 Debug Frontend (Attach to Chrome)",
            "type": "chrome",
            "request": "attach",
            "port": 9222,
            "webRoot": "${workspaceFolder}/static",
            "sourceMaps": true
        },
       
    ],
    "compounds": [
        {
            "name": "🔥 Debug Full Stack (Backend + Frontend)",
            "configurations": [
                "🚀 Launch FOIS Chatbot (Port 5001)",
                "🌐 Debug Frontend (Chrome)"
            ],
            "stopAll": true,
            "presentation": {
                "hidden": false,
                "group": "fullstack",
                "order": 1
            }
        }
    ]
}