/* Reset và Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    overflow: hidden;
}

/* Chat Container */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 800px;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);
}

/* Header */
.chat-header {
    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
    color: white;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.company-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.company-logo {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.company-details h1 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.2rem;
}

.company-details p {
    font-size: 0.9rem;
    opacity: 0.9;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Company Info Panel */
.company-panel {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem;
    transition: all 0.3s ease;
}

.company-panel.hidden {
    display: none;
}

.panel-content h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #555;
}

.info-item i {
    color: #3498db;
    width: 20px;
}

/* Chat Messages */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: #f8f9fa;
}

.welcome-message {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    animation: fadeInUp 0.5s ease;
}

.bot-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #3498db, #2c3e50);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.message-content {
    background: white;
    padding: 1rem;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    flex: 1;
}

.message-content h3 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.message-content ul {
    margin: 1rem 0;
    padding-left: 1rem;
}

.message-content li {
    margin: 0.5rem 0;
    color: #555;
}

.message {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    animation: fadeInUp 0.3s ease;
}

.message.user {
    flex-direction: row-reverse;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.message.user .message-content {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

/* Suggestions */
.suggestions {
    background: white;
    border-top: 1px solid #e9ecef;
    padding: 1rem;
}

.suggestions-title {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.suggestions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.suggestion-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #555;
}

.suggestion-item:hover {
    background: #3498db;
    color: white;
    transform: translateY(-2px);
}

/* Chat Input */
.chat-input {
    background: white;
    border-top: 1px solid #e9ecef;
    padding: 1rem;
}

.input-container {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

#messageInput {
    flex: 1;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    outline: none;
    transition: all 0.3s ease;
}

#messageInput:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.send-button {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-button:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.input-footer {
    text-align: center;
    margin-top: 0.5rem;
}

.powered-by {
    font-size: 0.8rem;
    color: #999;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    animation: fadeInUp 0.3s ease;
}

.typing-indicator.hidden {
    display: none;
}

.typing-dots {
    background: white;
    padding: 1rem;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 0.3rem;
    align-items: center;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background: #3498db;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-overlay.hidden {
    display: none;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-spinner i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #3498db;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-10px);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .chat-container {
        height: 100vh;
        max-width: 100%;
    }
    
    .company-details h1 {
        font-size: 1rem;
    }
    
    .company-details p {
        font-size: 0.8rem;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .suggestions-list {
        flex-direction: column;
    }
    
    .suggestion-item {
        text-align: center;
    }
}

/* Scrollbar Styling */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mt-1 { margin-top: 1rem; }
.mb-1 { margin-bottom: 1rem; }
.p-1 { padding: 1rem; }
