/* Language Selector Styles */
.language-selector {
    margin-bottom: var(--space-md);
    position: relative;
    z-index: 100;
}

.lang-dropdown {
    position: relative;
    width: 100%;
}

.lang-current {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: var(--space-sm) var(--space-md);
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-color);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.lang-current:hover {
    background: var(--hover-color);
    border-color: var(--primary-color);
}

.lang-current:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color-alpha);
}

.lang-flag {
    font-size: 16px;
    margin-right: var(--space-sm);
}

.lang-name {
    flex: 1;
    text-align: left;
    font-weight: 500;
}

.lang-current i {
    font-size: 12px;
    color: var(--text-secondary);
    transition: transform 0.2s ease;
}

.lang-dropdown.show .lang-current i {
    transform: rotate(180deg);
}

.lang-options {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.2s ease;
    margin-bottom: 4px;
    max-height: 200px;
    overflow-y: auto;
}

.lang-options.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Dropdown direction classes */
.lang-options.dropdown-down {
    top: 100%;
    bottom: auto;
    margin-top: 4px;
    margin-bottom: 0;
    transform: translateY(-10px);
}

.lang-options.dropdown-down.show {
    transform: translateY(0);
}

.lang-options.dropdown-up {
    bottom: 100%;
    top: auto;
    margin-bottom: 4px;
    margin-top: 0;
    transform: translateY(10px);
}

.lang-options.dropdown-up.show {
    transform: translateY(0);
}

.lang-option {
    display: flex;
    align-items: center;
    padding: var(--space-sm) var(--space-md);
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid var(--border-color);
}

.lang-option:last-child {
    border-bottom: none;
}

.lang-option:hover {
    background: var(--hover-color);
}

.lang-option.active {
    background: var(--primary-color-alpha);
    color: var(--primary-color);
    font-weight: 600;
}

.lang-option .lang-flag {
    margin-right: var(--space-sm);
}

.lang-option .lang-name {
    font-size: 14px;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .language-selector {
        margin-bottom: var(--space-sm);
    }

    .lang-current {
        padding: var(--space-xs) var(--space-sm);
        font-size: 13px;
    }

    .lang-option {
        padding: var(--space-xs) var(--space-sm);
    }

    .lang-option .lang-name {
        font-size: 13px;
    }

    /* Force dropdown to open upward on mobile */
    .lang-options {
        bottom: 100% !important;
        top: auto !important;
        margin-bottom: 4px !important;
        margin-top: 0 !important;
        max-height: 50vh;
    }
}

/* Dark theme adjustments */
[data-theme="dark"] .lang-current {
    background: var(--surface-dark);
    border-color: var(--border-dark);
    color: var(--text-dark);
}

[data-theme="dark"] .lang-current:hover {
    background: var(--hover-dark);
    border-color: var(--primary-color);
}

[data-theme="dark"] .lang-options {
    background: var(--surface-dark);
    border-color: var(--border-dark);
}

[data-theme="dark"] .lang-option {
    border-color: var(--border-dark);
}

[data-theme="dark"] .lang-option:hover {
    background: var(--hover-dark);
}

[data-theme="dark"] .lang-option.active {
    background: var(--primary-color-alpha);
    color: var(--primary-color);
}

/* Animation for language change */
.language-transition {
    transition: opacity 0.3s ease;
}

.language-transition.changing {
    opacity: 0.7;
}

/* Language indicator in header */
.header-language {
    display: flex;
    align-items: center;
    margin-left: var(--space-sm);
    padding: var(--space-xs) var(--space-sm);
    background: var(--surface-color);
    border-radius: var(--radius-sm);
    font-size: 12px;
    color: var(--text-secondary);
}

.header-language .lang-flag {
    margin-right: 4px;
}

/* Tooltip for language options */
.lang-option[title] {
    position: relative;
}

.lang-option[title]:hover::after {
    content: attr(title);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: var(--background-dark);
    color: var(--text-light);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: 11px;
    white-space: nowrap;
    z-index: 1001;
    margin-left: var(--space-xs);
    box-shadow: var(--shadow-md);
}

/* Loading state for language change */
.lang-loading {
    position: relative;
    pointer-events: none;
}

.lang-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: var(--space-md);
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* Accessibility improvements */
.lang-current:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.lang-option:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: -2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .lang-current {
        border-width: 2px;
    }
    
    .lang-option.active {
        background: var(--primary-color);
        color: var(--white);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .lang-current,
    .lang-options,
    .lang-option {
        transition: none;
    }
    
    .lang-current i {
        transition: none;
    }
    
    .lang-loading::after {
        animation: none;
    }
}
