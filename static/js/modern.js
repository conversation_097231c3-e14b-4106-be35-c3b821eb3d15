// Modern FOIS Chatbot JavaScript
class ModernChatBot {
    constructor() {
        console.log('🚀 Initializing Modern ChatBot...');

        // Initialize i18n first
        this.i18n = new I18n();
        console.log('🌍 I18n instance created');

        // DOM Elements
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');

        // Debug: Check if messageInput exists
        console.log('🔍 messageInput element:', this.messageInput);
        console.log('🔍 messageInput ID:', this.messageInput?.id);
        console.log('🔍 messageInput tagName:', this.messageInput?.tagName);
        this.messagesContainer = document.getElementById('messagesContainer');
        this.typingIndicator = document.getElementById('typingIndicator');
        this.suggestionsList = document.getElementById('suggestionsList');
        this.suggestionsContainer = document.getElementById('suggestionsContainer');

        console.log('🔍 Suggestions elements:', {
            suggestionsList: this.suggestionsList,
            suggestionsContainer: this.suggestionsContainer
        });
        this.clearChatBtn = document.getElementById('clearChat');
        this.mobileMenuBtn = document.getElementById('mobileMenuBtn');
        this.sidebar = document.getElementById('sidebar');
        this.sidebarToggle = document.getElementById('sidebarToggle');

        // CV Upload elements
        this.cvUploadModal = document.getElementById('cvUploadModal');
        this.modalOverlay = document.getElementById('modalOverlay');
        this.modalClose = document.getElementById('modalClose');
        this.uploadArea = document.getElementById('uploadArea');
        this.cvFileInput = document.getElementById('cvFileInput');
        this.selectFileBtn = document.getElementById('selectFileBtn');
        this.uploadProgress = document.getElementById('uploadProgress');
        this.uploadResult = document.getElementById('uploadResult');
        this.uploadBtn = document.getElementById('uploadBtn');
        this.cancelUpload = document.getElementById('cancelUpload');

        // Token tracking elements
        this.tokenToggle = document.getElementById('tokenToggle');
        this.tokenUsageContainer = document.getElementById('tokenUsageContainer');
        this.resetTokens = document.getElementById('resetTokens');
        this.totalTokensEl = document.getElementById('totalTokens');
        this.inputTokensEl = document.getElementById('inputTokens');
        this.outputTokensEl = document.getElementById('outputTokens');
        this.messageCountEl = document.getElementById('messageCount');
        this.lastMessageTokensEl = document.getElementById('lastMessageTokens');
        this.avgTokensPerMessageEl = document.getElementById('avgTokensPerMessage');

        // State
        this.isTyping = false;
        this.messageHistory = [];
        this.isClearing = false; // Prevent multiple clear operations

        // Vector API configuration
        this.useVectorAPI = localStorage.getItem('useVectorAPI') === 'true' || false;
        this.vectorAPIEndpoint = '/api/chat-vector';
        this.regularAPIEndpoint = '/api/chat';

        // Token tracking state
        this.tokenStats = {
            totalTokens: 0,
            inputTokens: 0,
            outputTokens: 0,
            messageCount: 0,
            lastMessageTokens: 0,
            tokenHistory: []
        };
        this.tokenDisplayVisible = false;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSuggestions();
        this.loadConversation();
        this.setupAutoResize();
        this.setupQuickActions();
        this.setupCVUpload();
        this.loadTokenStats(); // Initialize token tracking
        this.initializeTheme();
        this.initializeI18n();
        this.setupVectorAPIToggle(); // Setup vector API toggle

        console.log('✅ Modern ChatBot initialized successfully');
    }

    // Helper method to get current API endpoint
    getCurrentAPIEndpoint() {
        return this.useVectorAPI ? this.vectorAPIEndpoint : this.regularAPIEndpoint;
    }

    // Toggle between vector and regular API
    toggleVectorAPI() {
        this.useVectorAPI = !this.useVectorAPI;
        localStorage.setItem('useVectorAPI', this.useVectorAPI.toString());

        // Show user feedback
        const mode = this.useVectorAPI ? 'Vector AI' : 'Regular Chat';
        const emoji = this.useVectorAPI ? '🎯' : '💬';

        console.log(`🔄 Switched to ${mode}`);

        // Show a subtle notification
        this.showModeChangeNotification(mode, emoji);

        this.updateVectorAPIIndicator();
    }

    // Show mode change notification
    showModeChangeNotification(mode, emoji) {
        // Remove any existing notification
        const existingNotification = document.querySelector('.mode-notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'mode-notification';
        notification.innerHTML = `
            <span class="mode-emoji">${emoji}</span>
            <span class="mode-text">Switched to ${mode}</span>
        `;

        // Add notification styles
        const style = document.createElement('style');
        style.textContent = `
            .mode-notification {
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(0, 122, 255, 0.95);
                color: white;
                padding: 12px 20px;
                border-radius: 25px;
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 14px;
                font-weight: 500;
                box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
                z-index: 10000;
                animation: slideInNotification 0.3s ease-out;
                backdrop-filter: blur(10px);
            }

            .mode-emoji {
                font-size: 16px;
            }

            @keyframes slideInNotification {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOutNotification {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }

            .mode-notification.hiding {
                animation: slideOutNotification 0.3s ease-in;
            }

            /* Dark theme support */
            [data-theme="dark"] .mode-notification {
                background: rgba(74, 158, 255, 0.95);
                box-shadow: 0 4px 20px rgba(74, 158, 255, 0.3);
            }
        `;

        // Only add styles once
        if (!document.querySelector('#mode-notification-styles')) {
            style.id = 'mode-notification-styles';
            document.head.appendChild(style);
        }

        // Add to page
        document.body.appendChild(notification);

        // Auto-hide after 2 seconds
        setTimeout(() => {
            notification.classList.add('hiding');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, 2000);
    }

    // Setup vector API toggle functionality
    setupVectorAPIToggle() {
        // Create toggle button if it doesn't exist
        let vectorToggle = document.getElementById('vectorAPIToggle');
        if (!vectorToggle) {
            // Add toggle to toolbar or create a new one
            this.createVectorAPIToggle();
        } else {
            vectorToggle.addEventListener('click', () => this.toggleVectorAPI());
        }
        this.updateVectorAPIIndicator();
    }

    // Create vector API toggle button
    createVectorAPIToggle() {
        // Find the header actions container
        const headerActions = document.querySelector('.header-actions');
        if (headerActions) {
            const toggleContainer = document.createElement('div');
            toggleContainer.className = 'vector-api-toggle-container';
            toggleContainer.innerHTML = `
                <div class="modern-toggle" id="vectorAPIToggle" title="Toggle between Vector AI and Regular chat">
                    <span class="toggle-label">Vector</span>
                    <div class="toggle-switch">
                        <div class="toggle-slider">
                            <div class="toggle-circle"></div>
                        </div>
                    </div>
                </div>
            `;

            // Add modern styling
            const style = document.createElement('style');
            style.textContent = `
                .vector-api-toggle-container {
                    display: flex;
                    align-items: center;
                    margin-right: 12px;
                }

                .modern-toggle {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    cursor: pointer;
                    user-select: none;
                    transition: all 0.3s ease;
                }

                .toggle-label {
                    font-size: 14px;
                    font-weight: 500;
                    color: var(--text-color, #333);
                    transition: color 0.3s ease;
                }

                .toggle-switch {
                    position: relative;
                    width: 50px;
                    height: 28px;
                    background: #e0e0e0;
                    border-radius: 20px;
                    transition: all 0.3s ease;
                    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
                }

                .toggle-slider {
                    position: absolute;
                    top: 2px;
                    left: 2px;
                    width: 24px;
                    height: 24px;
                    background: white;
                    border-radius: 50%;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
                }

                .toggle-circle {
                    width: 100%;
                    height: 100%;
                    border-radius: 50%;
                    background: white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 10px;
                    transition: all 0.3s ease;
                }

                /* Active state - Vector mode */
                .modern-toggle.active .toggle-switch {
                    background: linear-gradient(135deg, #007AFF, #0056CC);
                    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1), 0 0 0 2px rgba(0, 122, 255, 0.2);
                }

                .modern-toggle.active .toggle-slider {
                    transform: translateX(22px);
                    background: white;
                }

                .modern-toggle.active .toggle-label {
                    color: #007AFF;
                    font-weight: 600;
                }

                .modern-toggle.active .toggle-circle::before {
                    content: "🎯";
                    font-size: 10px;
                }

                /* Inactive state - Regular mode */
                .modern-toggle:not(.active) .toggle-circle::before {
                    content: "💬";
                    font-size: 10px;
                }

                /* Hover effects */
                .modern-toggle:hover .toggle-switch {
                    transform: scale(1.05);
                }

                .modern-toggle:hover .toggle-label {
                    color: #007AFF;
                }

                /* Dark theme support */
                [data-theme="dark"] .toggle-label {
                    color: var(--text-color, #e0e0e0);
                }

                [data-theme="dark"] .toggle-switch {
                    background: #404040;
                }

                [data-theme="dark"] .modern-toggle:hover .toggle-label {
                    color: #4A9EFF;
                }

                /* Responsive design */
                @media (max-width: 768px) {
                    .toggle-label {
                        display: none;
                    }

                    .vector-api-toggle-container {
                        margin-right: 8px;
                    }
                }

                /* Animation for state change */
                @keyframes toggleBounce {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.1); }
                    100% { transform: scale(1); }
                }

                .modern-toggle.changing {
                    animation: toggleBounce 0.3s ease;
                }
            `;
            document.head.appendChild(style);

            // Insert before the first button in header actions
            headerActions.insertBefore(toggleContainer, headerActions.firstChild);

            // Add event listener
            const toggleButton = document.getElementById('vectorAPIToggle');
            toggleButton.addEventListener('click', () => this.toggleVectorAPI());
        }
    }

    // Update vector API indicator
    updateVectorAPIIndicator() {
        const toggleButton = document.getElementById('vectorAPIToggle');
        const toggleLabel = toggleButton?.querySelector('.toggle-label');

        if (toggleButton) {
            // Add animation class for smooth transition
            toggleButton.classList.add('changing');

            if (this.useVectorAPI) {
                toggleButton.classList.add('active');
                if (toggleLabel) {
                    toggleLabel.textContent = 'Vector';
                }
            } else {
                toggleButton.classList.remove('active');
                if (toggleLabel) {
                    toggleLabel.textContent = 'Regular';
                }
            }

            // Remove animation class after animation completes
            setTimeout(() => {
                toggleButton.classList.remove('changing');
            }, 300);
        }

        console.log(`🎯 API Mode: ${this.useVectorAPI ? 'Vector' : 'Regular'}`);
    }

    removeEventListeners() {
        // Clone and replace elements to remove all event listeners
        if (this.clearChatBtn) {
            const newClearBtn = this.clearChatBtn.cloneNode(true);
            this.clearChatBtn.parentNode.replaceChild(newClearBtn, this.clearChatBtn);
            this.clearChatBtn = newClearBtn;
        }
    }

    setupEventListeners() {
        // Remove existing listeners to prevent duplicates
        this.removeEventListeners();

        // Send message
        this.sendButton?.addEventListener('click', () => this.sendMessage());
        this.messageInput?.addEventListener('keydown', (e) => {
            console.log(`⌨️ Key pressed: ${e.key}, shiftKey: ${e.shiftKey}`);
            if (e.key === 'Enter' && !e.shiftKey) {
                console.log('🚀 Enter pressed, calling sendMessage()');
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Attach file button
        const attachFileBtn = document.getElementById('attachFileBtn');
        if (attachFileBtn) {
            attachFileBtn.addEventListener('click', () => this.handleAttachFile());
        }

        // Theme toggle button
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // Token usage toggle button
        this.tokenToggle?.addEventListener('click', () => this.toggleTokenDisplay());

        // Reset tokens button
        this.resetTokens?.addEventListener('click', () => this.resetTokenStats());

        // Mobile menu
        this.mobileMenuBtn?.addEventListener('click', () => this.toggleSidebar());
        this.sidebarToggle?.addEventListener('click', () => this.toggleSidebar());

        // Clear chat - use proper event handling to prevent loops
        this.clearChatBtn?.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.clearChat();
        });

        // Click outside sidebar to close on mobile
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 768 && 
                this.sidebar?.classList.contains('open') &&
                !this.sidebar.contains(e.target) &&
                !this.mobileMenuBtn?.contains(e.target)) {
                this.toggleSidebar();
            }
        });

        // Window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768) {
                this.sidebar?.classList.remove('open');
            }
        });
    }

    setupAutoResize() {
        if (!this.messageInput) return;

        this.messageInput.addEventListener('input', () => {
            this.messageInput.style.height = 'auto';
            const newHeight = Math.min(this.messageInput.scrollHeight, 120);
            this.messageInput.style.height = newHeight + 'px';

            // Toggle multiline class based on height
            const inputField = this.messageInput.closest('.input-field');
            if (inputField) {
                if (newHeight > 34) {  // More than single line (34px = min-height)
                    inputField.classList.add('multiline');
                } else {
                    inputField.classList.remove('multiline');
                }
            }
        });
    }

    setupQuickActions() {
        // Sidebar action items
        const actionItems = document.querySelectorAll('.action-item');
        actionItems.forEach(item => {
            item.addEventListener('click', () => {
                const action = item.dataset.action;
                this.handleQuickAction(action);
            });
        });

        // Welcome feature items
        const featureItems = document.querySelectorAll('.feature-item');
        featureItems.forEach(item => {
            item.addEventListener('click', () => {
                const action = item.getAttribute('data-action');
                if (action) {
                    this.handleQuickAction(action);
                }
            });
        });
    }

    handleQuickAction(action) {
        const actions = {
            company: this.i18n.t('quick.company'),
            jobs: this.i18n.t('quick.jobs'),
            salary: this.i18n.t('quick.salary'),
            guide: this.i18n.t('quick.guide'),
            'upload-cv': () => this.showCVUploadModal()
        };

        const actionValue = actions[action];
        console.log(`🎯 Action triggered: "${action}" with value:`, actionValue);

        if (typeof actionValue === 'function') {
            actionValue();
        } else if (actionValue && this.messageInput) {
            this.messageInput.value = actionValue;
            console.log(`📝 Set input value from action: "${this.messageInput.value}"`);
            this.sendMessage();
        }
    }

    toggleSidebar() {
        if (this.sidebar) {
            this.sidebar.classList.toggle('open');
        }
    }

    clearMessageInput() {
        if (this.messageInput) {
            console.log(`🧹 Clearing message input. Current value: "${this.messageInput.value}"`);
            this.messageInput.value = '';
            this.messageInput.style.height = 'auto';

            // Force focus back to input
            setTimeout(() => {
                if (this.messageInput) {
                    this.messageInput.focus();
                }
            }, 100);

            console.log(`✅ Message input cleared. New value: "${this.messageInput.value}"`);
        } else {
            console.warn('⚠️ messageInput not found when trying to clear');
        }
    }

    forceClearInput() {
        // Force clear input with multiple methods
        console.log('🔧 Force clearing input with multiple methods...');

        if (this.messageInput) {
            // Method 1: Direct value assignment
            this.messageInput.value = '';

            // Method 2: Set attribute
            this.messageInput.setAttribute('value', '');

            // Method 3: Trigger input event
            this.messageInput.dispatchEvent(new Event('input', { bubbles: true }));

            // Method 4: Reset height
            this.messageInput.style.height = 'auto';

            // Method 5: Force focus and blur
            this.messageInput.focus();
            this.messageInput.blur();
            this.messageInput.focus();

            console.log(`✅ Force clear completed. Final value: "${this.messageInput.value}"`);
        }
    }

    async sendMessage() {
        const message = this.messageInput?.value.trim();
        console.log(`📝 sendMessage called with: "${message}"`);

        if (!message || this.isTyping) {
            console.log(`🚫 sendMessage blocked: message="${message}", isTyping=${this.isTyping}`);
            return;
        }

        // Add user message
        this.addMessage(message, 'user');

        // Clear input immediately - FORCE CLEAR with multiple attempts
        if (this.messageInput) {
            console.log(`🧹 FORCE clearing input. Before: "${this.messageInput.value}"`);

            // Immediate clear
            this.messageInput.value = '';
            this.messageInput.style.height = 'auto';

            // Force clear with setTimeout (in case of timing issues)
            setTimeout(() => {
                if (this.messageInput) {
                    this.messageInput.value = '';
                    this.messageInput.style.height = 'auto';
                    console.log(`🔄 Delayed clear. Value: "${this.messageInput.value}"`);
                }
            }, 0);

            // Another attempt after 50ms
            setTimeout(() => {
                if (this.messageInput) {
                    this.messageInput.value = '';
                    console.log(`🔄 Final clear check. Value: "${this.messageInput.value}"`);
                }
            }, 50);

            console.log(`✅ FORCE cleared input. After: "${this.messageInput.value}"`);
        } else {
            console.error('❌ messageInput is null/undefined!');
        }

        this.hideSuggestions();

        // Show typing indicator
        this.showTyping();

        try {
            const currentLanguage = this.i18n ? this.i18n.getCurrentLanguage() : 'vi';
            const apiEndpoint = this.getCurrentAPIEndpoint();
            console.log(`🌍 Sending message with language: ${currentLanguage} to ${apiEndpoint}`);  // Debug log
            const response = await fetch(apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    language: currentLanguage
                })
            });

            const data = await response.json();

            if (response.ok) {
                // Check if callback is needed
                if (data.needs_callback) {
                    // Show thinking message immediately
                    this.hideTyping();
                    this.addMessage(data.thinking_message, 'bot', false, 'thinking');

                    // Show typing indicator again for callback
                    this.showTyping();

                    // Call callback endpoint
                    await this.handleCallback(data);
                } else {
                    // Normal response without callback
                    this.hideTyping();
                    const responseMessage = data.response;
                    this.addMessage(responseMessage, 'bot');

                    // Track token usage if available
                    console.log('🔍 Token usage data received:', data.token_usage);
                    if (data.token_usage) {
                        this.updateTokenUsage(data.token_usage);
                    } else {
                        console.warn('⚠️ No token usage data in response');
                    }

                    // Display suggest questions if available
                    if (data.suggest_questions && data.suggest_questions.length > 0) {
                        console.log('📝 Displaying suggest questions:', data.suggest_questions);
                        this.displaySuggestions(data.suggest_questions);
                        this.showSuggestions();
                    } else {
                        console.log('❌ No suggest questions in response');
                    }
                }
            } else {
                this.hideTyping();
                this.addMessage(data.error || this.i18n.t('message.error_general'), 'bot', true);
            }
        } catch (error) {
            this.hideTyping();
            this.addMessage(this.i18n.t('message.error_connection'), 'bot', true);
            console.error('Error:', error);
        } finally {
            // Double-check that input is cleared
            if (this.messageInput && this.messageInput.value.trim()) {
                console.warn(`⚠️ Input not cleared properly, forcing clear. Current value: "${this.messageInput.value}"`);
                this.clearMessageInput();
            }
        }
    }

    async handleCallback(initialData) {
        try {
            // Wait a bit to simulate processing time
            await new Promise(resolve => setTimeout(resolve, 2000));

            const currentLanguage = this.i18n ? this.i18n.getCurrentLanguage() : 'vi';
            const response = await fetch('/api/chat/callback', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: initialData.user_id,
                    user_intent: initialData.user_intent,
                    language: currentLanguage
                })
            });

            const data = await response.json();

            if (response.ok) {
                this.hideTyping();

                // Replace thinking message with actual response
                this.replaceThinkingMessage(data.response);

                // Track token usage if available
                if (data.token_usage) {
                    this.updateTokenUsage(data.token_usage);
                }

                // Display suggest questions if available
                if (data.suggest_questions && data.suggest_questions.length > 0) {
                    console.log('📝 Displaying callback suggest questions:', data.suggest_questions);
                    this.displaySuggestions(data.suggest_questions);
                    this.showSuggestions();
                }
            } else {
                this.hideTyping();
                this.addMessage(data.error || 'Có lỗi xảy ra khi xử lý yêu cầu.', 'bot', true);
            }
        } catch (error) {
            this.hideTyping();
            this.addMessage('Không thể hoàn thành yêu cầu. Vui lòng thử lại.', 'bot', true);
            console.error('Callback Error:', error);
        }
    }

    handleAttachFile() {
        // Create file input element
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.pdf';
        fileInput.style.display = 'none';

        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                this.uploadPDF(file);
            }
        });

        // Trigger file selection
        document.body.appendChild(fileInput);
        fileInput.click();
        document.body.removeChild(fileInput);
    }

    async uploadPDF(file) {
        // Validate file
        if (!file.type.includes('pdf')) {
            this.addMessage(`❌ ${this.i18n.t('pdf.error_format')}`, 'bot', true);
            return;
        }

        if (file.size > 10 * 1024 * 1024) { // 10MB
            this.addMessage(`❌ ${this.i18n.t('pdf.error_size')}`, 'bot', true);
            return;
        }

        // Show upload progress
        this.addMessage(`📄 ${this.i18n.t('pdf.processing')} "${file.name}"...`, 'bot', false, 'thinking');
        this.showTyping();

        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/api/upload-pdf', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            this.hideTyping();

            if (response.ok && data.success) {
                // Replace thinking message with success message
                this.replaceThinkingMessage(`✅ ${data.message}`);

                // Prepare message to send to chatbot
                const pdfMessage = `Tôi vừa upload file PDF "${data.filename}". Đây là nội dung của file:\n\n${data.text_content}\n\nVui lòng phân tích và tư vấn cho tôi.`;

                // Add user message showing the PDF content
                this.addMessage(`📄 Đã upload file: ${data.filename}`, 'user');

                // Send the PDF content to chatbot
                await this.sendPDFContent(pdfMessage);

            } else {
                this.replaceThinkingMessage(`❌ ${data.error || 'Có lỗi xảy ra khi xử lý file PDF'}`);
            }

        } catch (error) {
            this.hideTyping();
            this.replaceThinkingMessage('❌ Không thể upload file. Vui lòng thử lại.');
            console.error('PDF Upload Error:', error);
        }
    }

    async sendPDFContent(pdfMessage) {
        // Show typing indicator for AI processing
        this.showTyping();

        try {
            const currentLanguage = this.i18n ? this.i18n.getCurrentLanguage() : 'vi';
            const apiEndpoint = this.getCurrentAPIEndpoint();
            console.log(`📄 Sending PDF content to ${apiEndpoint}`);
            const response = await fetch(apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: pdfMessage,
                    language: currentLanguage
                })
            });

            const data = await response.json();

            if (response.ok) {
                // Check if callback is needed
                if (data.needs_callback) {
                    // Show thinking message immediately
                    this.hideTyping();
                    this.addMessage(data.thinking_message, 'bot', false, 'thinking');

                    // Show typing indicator again for callback
                    this.showTyping();

                    // Call callback endpoint
                    await this.handleCallback(data);
                } else {
                    // Normal response without callback
                    this.hideTyping();
                    this.addMessage(data.response, 'bot');

                    // Display suggest questions if available
                    if (data.suggest_questions && data.suggest_questions.length > 0) {
                        this.displaySuggestions(data.suggest_questions);
                        this.showSuggestions();
                    }
                }
            } else {
                this.hideTyping();
                this.addMessage(data.error || 'Có lỗi xảy ra khi xử lý nội dung PDF.', 'bot', true);
            }
        } catch (error) {
            this.hideTyping();
            this.addMessage('Không thể xử lý nội dung PDF. Vui lòng thử lại.', 'bot', true);
            console.error('PDF Content Processing Error:', error);
        }
    }

    addMessage(content, type, isError = false, messageType = 'normal') {
        if (!this.messagesContainer) return;

        const messageGroup = document.createElement('div');
        messageGroup.className = 'message-group';

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;

        // Add special class for thinking messages
        if (messageType === 'thinking') {
            messageDiv.classList.add('thinking-message');
            messageDiv.setAttribute('data-thinking', 'true');
        }

        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = type === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';

        const messageBubble = document.createElement('div');
        messageBubble.className = 'message-bubble';
        
        if (isError) {
            messageBubble.style.background = 'var(--error-color)';
            messageBubble.style.color = 'var(--white)';
        }
        
        messageBubble.innerHTML = this.formatMessage(content);
        
        messageContent.appendChild(messageBubble);
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);
        messageGroup.appendChild(messageDiv);
        
        this.messagesContainer.appendChild(messageGroup);
        this.scrollToBottom();
        
        // Store in history
        this.messageHistory.push({ type, content, timestamp: new Date() });
    }

    formatMessage(content) {
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n/g, '<br>')
            .replace(/(\d+\.\s)/g, '<br>$1')
            .replace(/([-•]\s)/g, '<br>$1');
    }

    replaceThinkingMessage(newContent) {
        // Find the last thinking message and replace its content
        const thinkingMessages = this.messagesContainer.querySelectorAll('[data-thinking="true"]');
        if (thinkingMessages.length > 0) {
            const lastThinkingMessage = thinkingMessages[thinkingMessages.length - 1];
            const messageBubble = lastThinkingMessage.querySelector('.message-bubble');
            if (messageBubble) {
                // Add fade-out effect
                messageBubble.style.transition = 'all 0.3s ease';
                messageBubble.style.opacity = '0.7';
                messageBubble.style.transform = 'scale(0.98)';

                setTimeout(() => {
                    // Remove thinking class and data attribute
                    lastThinkingMessage.classList.remove('thinking-message');
                    lastThinkingMessage.removeAttribute('data-thinking');

                    // Update content with markdown support
                    if (typeof marked !== 'undefined') {
                        messageBubble.innerHTML = marked.parse(newContent);
                    } else {
                        messageBubble.innerHTML = this.formatMessage(newContent);
                    }

                    // Fade back in with new content
                    messageBubble.style.opacity = '1';
                    messageBubble.style.transform = 'scale(1)';

                    // Clean up inline styles after animation
                    setTimeout(() => {
                        messageBubble.style.transition = '';
                        messageBubble.style.opacity = '';
                        messageBubble.style.transform = '';
                    }, 300);

                    this.scrollToBottom();
                }, 150);
            }
        }
    }

    showTyping() {
        this.isTyping = true;
        if (this.typingIndicator) {
            this.typingIndicator.classList.remove('hidden');
            // Force reflow for smooth animation
            this.typingIndicator.offsetHeight;
        }
        if (this.sendButton) {
            this.sendButton.disabled = true;
        }
        // No need to scroll since typing indicator is now above input
    }

    hideTyping() {
        this.isTyping = false;
        if (this.typingIndicator) {
            this.typingIndicator.classList.add('hidden');
        }
        if (this.sendButton) {
            this.sendButton.disabled = false;
        }
    }

    scrollToBottom() {
        setTimeout(() => {
            if (this.messagesContainer) {
                // Smooth scroll to bottom
                this.messagesContainer.scrollTo({
                    top: this.messagesContainer.scrollHeight,
                    behavior: 'smooth'
                });
            }
        }, 100);
    }

    async loadSuggestions() {
        try {
            const currentLang = this.i18n ? this.i18n.getCurrentLanguage() : 'vi';
            const response = await fetch(`/api/suggestions?lang=${currentLang}`);
            const data = await response.json();

            if (response.ok && data.suggestions) {
                this.displaySuggestions(data.suggestions);
            }
        } catch (error) {
            console.error('Error loading suggestions:', error);
        }
    }

    displaySuggestions(suggestions) {
        if (!this.suggestionsList) return;
        
        this.suggestionsList.innerHTML = '';
        
        suggestions.slice(0, 6).forEach(suggestion => {
            const suggestionItem = document.createElement('div');
            suggestionItem.className = 'suggestion-item';
            suggestionItem.textContent = suggestion;
            suggestionItem.addEventListener('click', () => {
                console.log(`💡 Suggestion clicked: "${suggestion}"`);
                if (this.messageInput) {
                    this.messageInput.value = suggestion;
                    console.log(`📝 Set input value to: "${this.messageInput.value}"`);
                    this.sendMessage();
                } else {
                    console.warn('⚠️ messageInput not found in suggestion click');
                }
            });
            
            this.suggestionsList.appendChild(suggestionItem);
        });
    }

    hideSuggestions() {
        if (this.suggestionsContainer) {
            console.log('🙈 Hiding suggestions');
            this.suggestionsContainer.style.display = 'none';
        }
    }

    showSuggestions() {
        if (this.suggestionsContainer) {
            console.log('👁️ Showing suggestions');
            this.suggestionsContainer.style.display = 'block';
        }
    }

    async clearChat() {
        // Prevent multiple simultaneous clear operations
        if (this.isClearing) {
            console.log('🚫 Clear operation already in progress');
            return;
        }

        // Show confirmation dialog
        const confirmMessage = this.i18n.t('message.clear_confirm');
        const userConfirmed = confirm(confirmMessage);

        if (!userConfirmed) {
            console.log('❌ User cancelled clear operation');
            return;
        }

        this.isClearing = true;

        // Disable button to prevent multiple clicks
        if (this.clearChatBtn) {
            this.clearChatBtn.disabled = true;
            this.clearChatBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Đang xóa...</span>';
        }

        console.log('🗑️ Starting clear chat operation');

        try {
            const response = await fetch('/api/clear', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                // Clear messages except welcome message
                const messageGroups = this.messagesContainer?.querySelectorAll('.message-group:not(:first-child)');
                messageGroups?.forEach(group => group.remove());

                this.showSuggestions();
                this.messageHistory = [];
                this.showNotification(this.i18n.t('message.clear_success'), 'success');
                console.log('✅ Chat cleared successfully');
            } else {
                this.showNotification(this.i18n.t('message.clear_error'), 'error');
                console.error('❌ Server error clearing chat:', response.status);
            }
        } catch (error) {
            this.showNotification(this.i18n.t('message.error_connection'), 'error');
            console.error('❌ Network error clearing chat:', error);
        } finally {
            // Always reset the flag and restore button
            this.isClearing = false;

            if (this.clearChatBtn) {
                this.clearChatBtn.disabled = false;
                this.clearChatBtn.innerHTML = '<i class="fas fa-trash"></i> <span data-i18n="sidebar.clear_history">Xóa lịch sử</span>';

                // Re-apply i18n to the restored button
                if (this.i18n) {
                    this.i18n.updateUI();
                }
            }

            console.log('🏁 Clear operation completed');
        }
    }

    async loadConversation() {
        try {
             this.messageHistory = [];
            const response = await fetch('/api/conversation');
            const data = await response.json();
            
            if (response.ok && data.conversation && data.conversation.length > 0) {
                data.conversation.forEach(msg => {
                    if (msg.type === 'user' || msg.type === 'bot') {
                        this.addMessage(msg.message, msg.type);
                    }
                });

                // Only hide suggestions if there's conversation history
                // They will be shown again when new suggest_questions arrive
                this.hideSuggestions();
            } else {
                // No conversation history, show default suggestions
                this.showSuggestions();
            }
        } catch (error) {
            console.error('Error loading conversation:', error);
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        const colors = {
            success: 'var(--success-color)',
            error: 'var(--error-color)',
            info: 'var(--primary-color)'
        };

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${colors[type]};
            color: var(--white);
            padding: var(--space-md) var(--space-lg);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            font-size: 14px;
            font-weight: 500;
            animation: slideInRight 0.3s ease;
            max-width: 300px;
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // CV Upload Methods
    setupCVUpload() {
        if (!this.cvUploadModal) return;

        // Modal controls
        this.modalOverlay?.addEventListener('click', () => this.hideCVUploadModal());
        this.modalClose?.addEventListener('click', () => this.hideCVUploadModal());
        this.cancelUpload?.addEventListener('click', () => this.hideCVUploadModal());

        // File selection
        this.selectFileBtn?.addEventListener('click', () => this.cvFileInput?.click());
        this.uploadArea?.addEventListener('click', () => this.cvFileInput?.click());
        this.cvFileInput?.addEventListener('change', (e) => this.handleFileSelect(e));

        // Drag and drop
        this.uploadArea?.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.uploadArea?.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        this.uploadArea?.addEventListener('drop', (e) => this.handleFileDrop(e));

        // Upload button
        this.uploadBtn?.addEventListener('click', () => this.uploadCV());
    }

    showCVUploadModal() {
        if (this.cvUploadModal) {
            this.cvUploadModal.classList.remove('hidden');
            this.resetUploadForm();
        }
    }

    hideCVUploadModal() {
        if (this.cvUploadModal) {
            this.cvUploadModal.classList.add('hidden');
            this.resetUploadForm();
        }
    }

    resetUploadForm() {
        if (this.cvFileInput) this.cvFileInput.value = '';
        if (this.uploadProgress) this.uploadProgress.classList.add('hidden');
        if (this.uploadResult) this.uploadResult.classList.add('hidden');
        if (this.uploadBtn) this.uploadBtn.classList.add('hidden');
        if (this.uploadArea) this.uploadArea.classList.remove('dragover');

        // Reset progress
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        if (progressFill) progressFill.style.width = '0%';
        if (progressText) progressText.textContent = 'Đang upload...';
    }

    handleDragOver(e) {
        e.preventDefault();
        this.uploadArea?.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        this.uploadArea?.classList.remove('dragover');
    }

    handleFileDrop(e) {
        e.preventDefault();
        this.uploadArea?.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.cvFileInput.files = files;
            this.handleFileSelect({ target: { files } });
        }
    }

    handleFileSelect(e) {
        const file = e.target.files[0];
        if (!file) return;

        // Validate file
        const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain', 'image/jpeg', 'image/png'];
        const maxSize = 10 * 1024 * 1024; // 10MB

        if (!allowedTypes.includes(file.type) && !file.name.match(/\.(pdf|docx|txt|jpg|jpeg|png)$/i)) {
            this.showNotification('Định dạng file không hỗ trợ. Chỉ chấp nhận: PDF, DOCX, TXT, JPG, PNG', 'error');
            return;
        }

        if (file.size > maxSize) {
            this.showNotification('File quá lớn. Tối đa 10MB', 'error');
            return;
        }

        // Show upload button
        if (this.uploadBtn) {
            this.uploadBtn.classList.remove('hidden');
            this.uploadBtn.innerHTML = `<i class="fas fa-upload"></i> Upload "${file.name}"`;
        }

        this.selectedFile = file;
    }

    async uploadCV() {
        if (!this.selectedFile) {
            this.showNotification('Vui lòng chọn file CV', 'error');
            return;
        }

        // Show progress
        if (this.uploadProgress) this.uploadProgress.classList.remove('hidden');
        if (this.uploadBtn) this.uploadBtn.disabled = true;

        const formData = new FormData();
        formData.append('cv_file', this.selectedFile);

        try {
            // Simulate progress
            this.updateProgress(20, 'Đang upload file...');

            const response = await fetch('/api/upload-cv', {
                method: 'POST',
                body: formData
            });

            this.updateProgress(60, 'Đang phân tích CV...');

            const result = await response.json();

            this.updateProgress(100, 'Hoàn tất!');

            if (result.success) {
                this.showUploadResult(result);
                this.showNotification('CV đã được phân tích thành công!', 'success');
            } else {
                this.showNotification(result.error || 'Có lỗi xảy ra khi phân tích CV', 'error');
            }

        } catch (error) {
            console.error('Upload error:', error);
            this.showNotification('Không thể upload CV. Vui lòng thử lại.', 'error');
        } finally {
            if (this.uploadBtn) this.uploadBtn.disabled = false;
        }
    }

    updateProgress(percent, text) {
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');

        if (progressFill) progressFill.style.width = `${percent}%`;
        if (progressText) progressText.textContent = text;
    }

    showUploadResult(result) {
        if (!this.uploadResult) return;

        const { cv_data, job_matches, recommendations } = result;

        let html = `
            <div class="result-header">
                <i class="fas fa-check-circle" style="color: var(--success-color);"></i>
                <h4>Phân tích CV thành công!</h4>
            </div>
        `;

        if (cv_data?.personal_info?.name && cv_data.personal_info.name !== 'Không xác định') {
            html += `<p><strong>Ứng viên:</strong> ${cv_data.personal_info.name}</p>`;
        }

        if (cv_data?.skills?.length > 0) {
            html += `<p><strong>Kỹ năng:</strong> ${cv_data.skills.slice(0, 5).join(', ')}</p>`;
        }

        if (job_matches?.length > 0) {
            html += `<h5 style="margin-top: var(--space-lg); margin-bottom: var(--space-md);">🎯 Công việc phù hợp (${job_matches.length}):</h5>`;

            job_matches.slice(0, 3).forEach(job => {
                const scoreClass = job.match_score >= 70 ? 'high' : job.match_score >= 50 ? 'medium' : 'low';
                html += `
                    <div class="job-match">
                        <div class="job-title">${job.title}</div>
                        <div class="job-company">${job.company} - ${job.location}</div>
                        <span class="match-score ${scoreClass}">${job.match_score}% phù hợp</span>
                    </div>
                `;
            });
        }

        if (recommendations) {
            html += `<div style="margin-top: var(--space-lg);"><strong>💡 Gợi ý:</strong><br>${recommendations.replace(/\n/g, '<br>')}</div>`;
        }

        this.uploadResult.innerHTML = html;
        this.uploadResult.classList.remove('hidden');

        // Add to chat
        this.addCVAnalysisToChat(result);
    }

    addCVAnalysisToChat(result) {
        const { job_matches, total_jobs_found } = result;

        let message = `🎉 CV của bạn đã được phân tích thành công!\n\n`;

        if (total_jobs_found > 0) {
            message += `🎯 Tìm thấy ${total_jobs_found} công việc phù hợp:\n\n`;

            job_matches.slice(0, 3).forEach((job, index) => {
                message += `${index + 1}. **${job.title}** tại ${job.company}\n`;
                message += `   📍 ${job.location} | 💰 ${job.salary}\n`;
                message += `   🎯 Độ phù hợp: ${job.match_score}%\n\n`;
            });

            if (job_matches.length > 3) {
                message += `... và ${job_matches.length - 3} công việc khác.\n\n`;
            }
        } else {
            message += `Hiện tại chưa có vị trí phù hợp hoàn toàn. Hãy cập nhật thêm kỹ năng!\n\n`;
        }

        message += `💡 Bạn có thể hỏi tôi thêm về bất kỳ vị trí nào!`;

        this.addMessage(message, 'bot');
    }

    // Theme Management
    initializeTheme() {
        // Check for saved theme preference or default to 'dark'
        const savedTheme = localStorage.getItem('chatbot-theme') || 'dark';
        this.setTheme(savedTheme);
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'dark';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }

    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('chatbot-theme', theme);

        // Update theme toggle icon
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('i');
            if (icon) {
                if (theme === 'dark') {
                    icon.className = 'fas fa-moon';
                    themeToggle.title = 'Chuyển sang giao diện sáng';
                } else {
                    icon.className = 'fas fa-sun';
                    themeToggle.title = 'Chuyển sang giao diện tối';
                }
            }
        }

        console.log(`🎨 Theme changed to: ${theme}`);
    }

    // I18n Management
    initializeI18n() {
        this.i18n.init();

        // Listen for language changes
        document.addEventListener('languageChanged', (e) => {
            this.onLanguageChanged(e.detail.language);
        });

        // Update welcome message on initial load
        this.updateWelcomeMessage();

        // Update button titles on initial load
        this.updateButtonTitles();

        console.log(`🌍 I18n initialized with language: ${this.i18n.getCurrentLanguage()}`);
    }

    onLanguageChanged(newLanguage) {
        console.log(`🌍 Language changed to: ${newLanguage}`);

        // Update placeholder text
        if (this.messageInput) {
            this.messageInput.placeholder = this.i18n.t('chat.placeholder');
        }

        // Update theme toggle title
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'dark';
            const titleKey = currentTheme === 'dark' ? 'theme.light' : 'theme.dark';
            themeToggle.title = this.i18n.t(titleKey);
        }

        // Update all button titles
        this.updateButtonTitles();

        // Update welcome message if it's the first message
        const welcomeMessage = this.messagesContainer?.querySelector('.welcome-message');
        if (welcomeMessage && this.messageHistory.length === 0) {
            // Update welcome header
            const welcomeHeader = welcomeMessage.querySelector('.welcome-header h3');
            if (welcomeHeader) {
                welcomeHeader.textContent = this.i18n.t('message.welcome');
            }

            // Update welcome text
            const welcomeText = welcomeMessage.querySelector('.welcome-text');
            if (welcomeText) {
                welcomeText.textContent = this.i18n.t('welcome.start_message');
            }
        }

        // Reload suggestions with new language
        this.loadSuggestions();

        // Update any CV upload modal content
        this.updateCVModalLanguage();
    }

    updateButtonTitles() {
        // Update all buttons with data-i18n-title attribute
        const buttonsWithI18nTitle = document.querySelectorAll('[data-i18n-title]');

        buttonsWithI18nTitle.forEach(button => {
            const titleKey = button.getAttribute('data-i18n-title');
            if (titleKey) {
                const translatedTitle = this.i18n.t(titleKey);
                button.title = translatedTitle;
                console.log(`🔄 Updated button title: ${button.id || button.className} -> "${translatedTitle}"`);
            }
        });

        // Special handling for language selector (since it's created by i18n.js)
        const langCurrent = document.getElementById('langCurrent');
        if (langCurrent) {
            const titleKey = langCurrent.getAttribute('data-i18n-title');
            if (titleKey) {
                const translatedTitle = this.i18n.t(titleKey);
                langCurrent.title = translatedTitle;
                console.log(`🌍 Updated language selector title: "${translatedTitle}"`);
            }
        }

        console.log(`✅ Updated ${buttonsWithI18nTitle.length} button titles`);
    }

    updateCVModalLanguage() {
        // Update CV upload modal if it exists
        const modalTitle = document.querySelector('#cvUploadModal .modal-title');
        if (modalTitle) {
            modalTitle.textContent = this.i18n.t('cv.modal_title');
        }

        const dragDropText = document.querySelector('#cvUploadModal .upload-text');
        if (dragDropText) {
            dragDropText.innerHTML = `${this.i18n.t('cv.drag_drop')} <strong>${this.i18n.t('cv.select_file')}</strong>`;
        }

        const supportedFormats = document.querySelector('#cvUploadModal .upload-hint');
        if (supportedFormats) {
            supportedFormats.textContent = this.i18n.t('cv.supported_formats');
        }
    }

    async loadWelcomeMessage(language) {
        try {
            const response = await fetch(`/api/welcome-message?lang=${language}`);
            const data = await response.json();

            if (response.ok && data.message) {
                const firstMessage = this.messagesContainer?.querySelector('.message-group:first-child .message-bubble');
                if (firstMessage) {
                    firstMessage.innerHTML = this.formatMessage(data.message);
                }
            }
        } catch (error) {
            console.error('Error loading welcome message:', error);
            // Fallback to i18n
            const firstMessage = this.messagesContainer?.querySelector('.message-group:first-child .message-bubble');
            if (firstMessage) {
                firstMessage.innerHTML = this.formatMessage(this.i18n.t('message.welcome'));
            }
        }
    }

    updateWelcomeMessage() {
        const welcomeMessage = this.messagesContainer?.querySelector('.welcome-message');
        if (welcomeMessage) {
            // Update welcome header
            const welcomeHeader = welcomeMessage.querySelector('.welcome-header h3');
            if (welcomeHeader) {
                welcomeHeader.textContent = this.i18n.t('message.welcome');
            }

            // Update welcome text
            const welcomeText = welcomeMessage.querySelector('.welcome-text');
            if (welcomeText) {
                welcomeText.textContent = this.i18n.t('welcome.start_message');
            }
        }
    }

    // Token tracking methods
    toggleTokenDisplay() {
        console.log('🔄 Toggling token display. Current state:', this.tokenDisplayVisible);
        this.tokenDisplayVisible = !this.tokenDisplayVisible;

        console.log('📊 New token display state:', this.tokenDisplayVisible);

        if (this.tokenDisplayVisible) {
            console.log('👁️ Showing token display');
            this.tokenUsageContainer?.classList.remove('hidden');
            this.tokenToggle?.classList.add('token-active');
            this.loadTokenStats();
            this.updateTokenDisplay(); // Force update display
        } else {
            console.log('🙈 Hiding token display');
            this.tokenUsageContainer?.classList.add('hidden');
            this.tokenToggle?.classList.remove('token-active');
        }

        // Save preference
        localStorage.setItem('tokenDisplayVisible', this.tokenDisplayVisible.toString());
    }

    resetTokenStats() {
        this.tokenStats = {
            totalTokens: 0,
            inputTokens: 0,
            outputTokens: 0,
            messageCount: 0,
            lastMessageTokens: 0,
            tokenHistory: []
        };

        this.updateTokenDisplay();
        this.saveTokenStats();

        // Show confirmation
        this.showNotification('Token statistics reset', 'success');
    }

    updateTokenUsage(tokenData) {
        console.log('📊 Updating token usage with data:', tokenData);
        if (!tokenData) {
            console.warn('⚠️ No token data provided to updateTokenUsage');
            return;
        }

        const inputTokens = tokenData.input_tokens || 0;
        const outputTokens = tokenData.output_tokens || 0;
        const totalMessageTokens = inputTokens + outputTokens;

        console.log(`📈 Token breakdown: Input=${inputTokens}, Output=${outputTokens}, Total=${totalMessageTokens}`);

        // Update stats
        this.tokenStats.inputTokens += inputTokens;
        this.tokenStats.outputTokens += outputTokens;
        this.tokenStats.totalTokens += totalMessageTokens;
        this.tokenStats.messageCount += 1;
        this.tokenStats.lastMessageTokens = totalMessageTokens;

        // Store in history
        this.tokenStats.tokenHistory.push({
            timestamp: new Date().toISOString(),
            inputTokens,
            outputTokens,
            totalTokens: totalMessageTokens
        });

        // Keep only last 100 messages in history
        if (this.tokenStats.tokenHistory.length > 100) {
            this.tokenStats.tokenHistory = this.tokenStats.tokenHistory.slice(-100);
        }

        this.updateTokenDisplay();
        this.saveTokenStats();
    }

    updateTokenDisplay() {
        console.log('🖥️ Updating token display. Visible:', this.tokenDisplayVisible);
        console.log('📊 Current token stats:', this.tokenStats);

        if (!this.tokenDisplayVisible) {
            console.log('👁️ Token display not visible, skipping update');
            return;
        }

        const avgTokens = this.tokenStats.messageCount > 0
            ? Math.round(this.tokenStats.totalTokens / this.tokenStats.messageCount)
            : 0;

        console.log('🧮 Calculated average tokens:', avgTokens);

        // Update display elements
        if (this.totalTokensEl) {
            this.totalTokensEl.textContent = this.tokenStats.totalTokens.toLocaleString();
            console.log('📈 Updated total tokens:', this.tokenStats.totalTokens);
        }
        if (this.inputTokensEl) {
            this.inputTokensEl.textContent = this.tokenStats.inputTokens.toLocaleString();
            console.log('📥 Updated input tokens:', this.tokenStats.inputTokens);
        }
        if (this.outputTokensEl) {
            this.outputTokensEl.textContent = this.tokenStats.outputTokens.toLocaleString();
            console.log('📤 Updated output tokens:', this.tokenStats.outputTokens);
        }
        if (this.messageCountEl) {
            this.messageCountEl.textContent = this.tokenStats.messageCount.toString();
            console.log('💬 Updated message count:', this.tokenStats.messageCount);
        }
        if (this.lastMessageTokensEl) {
            this.lastMessageTokensEl.textContent = `${this.tokenStats.lastMessageTokens} tokens`;
            console.log('🔄 Updated last message tokens:', this.tokenStats.lastMessageTokens);
        }
        if (this.avgTokensPerMessageEl) {
            this.avgTokensPerMessageEl.textContent = `${avgTokens} tokens`;
            console.log('📊 Updated average tokens:', avgTokens);
        }

        console.log('✅ Token display update completed');
    }

    saveTokenStats() {
        try {
            localStorage.setItem('tokenStats', JSON.stringify(this.tokenStats));
        } catch (error) {
            console.warn('Failed to save token stats:', error);
        }
    }

    loadTokenStats() {
        try {
            const saved = localStorage.getItem('tokenStats');
            if (saved) {
                this.tokenStats = { ...this.tokenStats, ...JSON.parse(saved) };
            }

            // Load display preference
            const displayPref = localStorage.getItem('tokenDisplayVisible');
            if (displayPref === 'true') {
                this.tokenDisplayVisible = true;
                this.tokenUsageContainer?.classList.remove('hidden');
                this.tokenToggle?.classList.add('token-active');
            }

            this.updateTokenDisplay();
        } catch (error) {
            console.warn('Failed to load token stats:', error);
        }
    }
}

// Initialize chatbot when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('🌍 DOM loaded, checking I18n availability...');
    if (typeof I18n !== 'undefined') {
        console.log('✅ I18n class available');
        window.chatbot = new ModernChatBot();
        console.log('🤖 Chatbot initialized and assigned to window.chatbot');
    } else {
        console.error('❌ I18n class not available');
    }
});



// Add notification animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
