// FOIS Chatbot Web Interface
class ChatBot {
    constructor() {
        console.log('🔧 Constructing ChatBot...');

        // Get DOM elements with error checking
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.chatMessages = document.getElementById('chatMessages');
        this.typingIndicator = document.getElementById('typingIndicator');
        this.suggestionsList = document.getElementById('suggestionsList');
        this.clearChatBtn = document.getElementById('clearChat');
        this.toggleInfoBtn = document.getElementById('toggleInfo');
        this.companyInfoPanel = document.getElementById('companyInfoPanel');
        this.loadingOverlay = document.getElementById('loadingOverlay');

        // Check if all required elements exist
        const requiredElements = {
            messageInput: this.messageInput,
            sendButton: this.sendButton,
            chatMessages: this.chatMessages,
            loadingOverlay: this.loadingOverlay
        };

        for (const [name, element] of Object.entries(requiredElements)) {
            if (!element) {
                console.error(`❌ Required element not found: ${name}`);
            }
        }

        this.isTyping = false;

        // Vector API configuration
        this.useVectorAPI = localStorage.getItem('useVectorAPI') === 'true' || false;
        this.vectorAPIEndpoint = '/api/chat-vector';
        this.regularAPIEndpoint = '/api/chat';

        this.init();
    }

    init() {
        console.log('🚀 Initializing ChatBot...');

        // Hide loading immediately
        this.hideLoading();

        this.setupEventListeners();
        this.loadSuggestions();
        this.loadConversation();

        console.log('✅ ChatBot initialized successfully');
    }

    // Helper method to get current API endpoint
    getCurrentAPIEndpoint() {
        return this.useVectorAPI ? this.vectorAPIEndpoint : this.regularAPIEndpoint;
    }

    setupEventListeners() {
        // Send message
        this.sendButton.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Clear chat
        this.clearChatBtn.addEventListener('click', () => this.clearChat());

        // Toggle company info
        this.toggleInfoBtn.addEventListener('click', () => this.toggleCompanyInfo());

        // Auto-resize input
        this.messageInput.addEventListener('input', () => this.autoResizeInput());
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message || this.isTyping) return;

        // Add user message to chat
        this.addMessage(message, 'user');
        this.messageInput.value = '';
        this.hideSuggestions();
        
        // Show typing indicator
        this.showTyping();
        
        try {
            const apiEndpoint = this.getCurrentAPIEndpoint();
            console.log(`📤 Sending message to ${apiEndpoint}`);
            const response = await fetch(apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: message })
            });

            const data = await response.json();
            
            if (response.ok) {
                // Hide typing and add bot response
                this.hideTyping();
                this.addMessage(data.response, 'bot');
            } else {
                this.hideTyping();
                this.addMessage(data.error || 'Có lỗi xảy ra, vui lòng thử lại.', 'bot', true);
            }
        } catch (error) {
            this.hideTyping();
            this.addMessage('Không thể kết nối đến server. Vui lòng thử lại.', 'bot', true);
            console.error('Error:', error);
        }
    }

    addMessage(content, type, isError = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        
        const avatar = document.createElement('div');
        avatar.className = type === 'user' ? 'user-avatar' : 'bot-avatar';
        avatar.innerHTML = type === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        if (isError) {
            messageContent.style.background = '#e74c3c';
            messageContent.style.color = 'white';
        }
        
        // Format message content (support for markdown-like formatting)
        messageContent.innerHTML = this.formatMessage(content);
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    formatMessage(content) {
        // Simple formatting for better display
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')  // Bold
            .replace(/\*(.*?)\*/g, '<em>$1</em>')              // Italic
            .replace(/\n/g, '<br>')                            // Line breaks
            .replace(/(\d+\.\s)/g, '<br>$1')                   // Numbered lists
            .replace(/([-•]\s)/g, '<br>$1');                   // Bullet points
    }

    showTyping() {
        this.isTyping = true;
        this.typingIndicator.classList.remove('hidden');
        this.sendButton.disabled = true;
        this.scrollToBottom();
    }

    hideTyping() {
        this.isTyping = false;
        this.typingIndicator.classList.add('hidden');
        this.sendButton.disabled = false;
    }

    scrollToBottom() {
        setTimeout(() => {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }, 100);
    }

    async loadSuggestions() {
        try {
            const response = await fetch('/api/suggestions');
            const data = await response.json();
            
            if (response.ok && data.suggestions) {
                this.displaySuggestions(data.suggestions);
            }
        } catch (error) {
            console.error('Error loading suggestions:', error);
        }
    }

    displaySuggestions(suggestions) {
        this.suggestionsList.innerHTML = '';
        
        suggestions.forEach(suggestion => {
            const suggestionItem = document.createElement('div');
            suggestionItem.className = 'suggestion-item';
            suggestionItem.textContent = suggestion;
            suggestionItem.addEventListener('click', () => {
                this.messageInput.value = suggestion;
                this.sendMessage();
            });
            
            this.suggestionsList.appendChild(suggestionItem);
        });
    }

    hideSuggestions() {
        const suggestions = document.querySelector('.suggestions');
        if (suggestions) {
            suggestions.style.display = 'none';
        }
    }

    async clearChat() {
        if (confirm('Bạn có chắc muốn xóa toàn bộ lịch sử chat?')) {
            try {
                const response = await fetch('/api/clear', {
                    method: 'POST'
                });
                
                if (response.ok) {
                    // Clear messages except welcome message
                    const messages = this.chatMessages.querySelectorAll('.message');
                    messages.forEach(msg => msg.remove());
                    
                    // Show suggestions again
                    const suggestions = document.querySelector('.suggestions');
                    if (suggestions) {
                        suggestions.style.display = 'block';
                    }
                    
                    this.showNotification('Đã xóa lịch sử chat', 'success');
                } else {
                    this.showNotification('Không thể xóa lịch sử chat', 'error');
                }
            } catch (error) {
                this.showNotification('Lỗi kết nối', 'error');
                console.error('Error clearing chat:', error);
            }
        }
    }

    toggleCompanyInfo() {
        this.companyInfoPanel.classList.toggle('hidden');
    }

    async loadConversation() {
        try {
            const response = await fetch('/api/conversation');
            const data = await response.json();
            
            if (response.ok && data.conversation && data.conversation.length > 0) {
                // Load previous messages
                data.conversation.forEach(msg => {
                    if (msg.type === 'user' || msg.type === 'bot') {
                        this.addMessage(msg.message, msg.type);
                    }
                });
                
                this.hideSuggestions();
            }
        } catch (error) {
            console.error('Error loading conversation:', error);
        }
    }

    autoResizeInput() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
            color: white;
            padding: 1rem;
            border-radius: 5px;
            z-index: 1000;
            animation: slideInRight 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    showLoading() {
        this.loadingOverlay.classList.remove('hidden');
    }

    hideLoading() {
        console.log('🔄 Hiding loading overlay...');
        if (this.loadingOverlay) {
            this.loadingOverlay.classList.add('hidden');
            this.loadingOverlay.style.display = 'none';
            console.log('✅ Loading overlay hidden');
        } else {
            console.warn('⚠️ Loading overlay element not found');
            // Try to find and hide any loading elements
            const loadingElements = document.querySelectorAll('.loading-overlay, .loading, [id*="loading"]');
            loadingElements.forEach(el => {
                el.style.display = 'none';
                el.classList.add('hidden');
            });
        }
    }
}

// Force hide loading immediately
document.addEventListener('DOMContentLoaded', () => {
    console.log('📄 DOM Content Loaded');

    // Force hide any loading overlays immediately
    const hideAllLoading = () => {
        const loadingElements = document.querySelectorAll('.loading-overlay, .loading, [id*="loading"], [class*="loading"]');
        loadingElements.forEach(el => {
            el.style.display = 'none';
            el.classList.add('hidden');
        });

        // Also hide body loading state if any
        document.body.classList.remove('loading');
        document.body.style.overflow = 'auto';

        console.log(`🔄 Force hidden ${loadingElements.length} loading elements`);
    };

    // Hide loading immediately
    hideAllLoading();

    // Initialize chatbot
    setTimeout(() => {
        new ChatBot();
    }, 100);
});

// Also try to hide loading as soon as possible
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    });
} else {
    // DOM already loaded
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
}

// Add CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
