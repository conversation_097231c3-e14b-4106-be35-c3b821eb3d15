#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script để chạy Email Chatbot với các tùy chọn khác nhau
"""

import argparse
import sys
from main import EmailChatbot
from gemini_ai import GeminiAI
from config import CHATBOT_SETTINGS, GEMINI_API_KEY, validate_config


def main():
    parser = argparse.ArgumentParser(description='Email Chatbot với AI')
    parser.add_argument('--no-ai', action='store_true',
                        help='Tắt AI, chỉ dùng phản hồi cơ bản')
    parser.add_argument('--interval', type=int,
                        default=CHATBOT_SETTINGS['CHECK_INTERVAL'],
                        help='Thời gian kiểm tra email (giây)')
    parser.add_argument('--test-only', action='store_true',
                        help='Chỉ test gửi email, không chạy chatbot')
    parser.add_argument('--test-ai', action='store_true',
                        help='Test AI responses')

    args = parser.parse_args()

    print("🚀 Email Chatbot với AI")
    print("=" * 50)

    # Kiểm tra cấu hình
    if not validate_config():
        print("\n❌ Cấu hình không đầy đủ!")
        print("💡 Hướng dẫn:")
        print("1. Copy file .env.example thành .env")
        print("2. Cập nhật các giá trị trong file .env")
        print("3. Chạy lại chatbot")
        return

    if not GEMINI_API_KEY and not args.no_ai:
        print("⚠️ Cảnh báo: Chưa cấu hình GEMINI_API_KEY")
        print("AI sẽ được tắt tự động")
        args.no_ai = True

    # Khởi tạo chatbot
    chatbot = EmailChatbot()

    # Cấu hình AI
    if args.no_ai:
        chatbot.toggle_ai(False)
        print("🤖 AI Mode: TẮT")
    else:
        print("🤖 AI Mode: BẬT")

    print(f"📧 Email: {chatbot.sender_email}")
    print(f"⏰ Interval: {args.interval}s")
    print("=" * 50)

    # Test AI nếu được yêu cầu
    if args.test_ai:
        print("\n🤖 Testing AI...")
        if chatbot.use_ai:
            try:
                response = chatbot.gemini_ai.generate_response(
                    "Xin chào, tôi muốn hỏi về dịch vụ",
                    CHATBOT_SETTINGS['TEST_EMAIL'],
                    "Test AI"
                )
                print("✅ AI hoạt động bình thường")
                print(f"📝 Sample response:\n{response[:100]}...")
            except Exception as e:
                print(f"❌ AI Error: {str(e)}")
        else:
            print("⚠️ AI đã bị tắt")
        return

    # Test gửi email
    if not args.test_only:
        print("\n🧪 Test gửi email...")
        test_result = chatbot.send_email(
            CHATBOT_SETTINGS['TEST_EMAIL'],
            "Test Email Chatbot",
            "Test email từ chatbot"
        )

        if not test_result:
            print("❌ Test gửi email thất bại!")
            print("Vui lòng kiểm tra cấu hình email")
            return

        print("✅ Test gửi email thành công!")

    if args.test_only:
        print("✅ Test hoàn tất!")
        return

    # Chạy chatbot
    print("\n" + "=" * 50)
    print("💡 Nhấn Ctrl+C để dừng chatbot")
    print("=" * 50)

    try:
        chatbot.run_chatbot(check_interval=args.interval)
    except KeyboardInterrupt:
        print("\n🛑 Chatbot đã dừng!")
    except Exception as e:
        print(f"\n❌ Lỗi: {str(e)}")


if __name__ == "__main__":
    main()
