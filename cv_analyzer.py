#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CV Analyzer - Phân tích CV và gợi ý công việc phù hợp
"""

from sample_data import JOB_POSITIONS
from gemini_ai import GeminiAI
import os
import re
import json
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import tempfile

# Optional CV processing libraries
CV_LIBRARIES_AVAILABLE = True
try:
    import PyPDF2
    from docx import Document
    from PIL import Image
    import magic
except ImportError:
    CV_LIBRARIES_AVAILABLE = False
    # Only show warning if CV functionality is actually used
    PyPDF2 = None
    Document = None
    Image = None
    magic = None


class CVAnalyzer:
    """Phân tích CV và gợi ý công việc"""

    def __init__(self):
        self.gemini = GeminiAI()
        self.supported_formats = ['.pdf', '.docx',
                                  '.txt', '.jpg', '.jpeg', '.png']
        self.max_file_size = 10 * 1024 * 1024  # 10MB
        self.libraries_available = CV_LIBRARIES_AVAILABLE

    def _check_cv_libraries(self):
        """Check if CV processing libraries are available and show warning if needed"""
        if not self.libraries_available:
            print(
                "⚠️ CV processing libraries not installed. CV upload features disabled.")
            print("   To enable CV upload: pip install -r requirements.txt")
            print("   (CV libraries are included in the standard requirements.txt)")
            return False
        return True

    def is_valid_file(self, filename: str, file_size: int) -> Tuple[bool, str]:
        """Kiểm tra file có hợp lệ không"""
        if file_size > self.max_file_size:
            return False, f"File quá lớn. Tối đa {self.max_file_size // (1024*1024)}MB"

        file_ext = Path(filename).suffix.lower()
        if file_ext not in self.supported_formats:
            return False, f"Định dạng không hỗ trợ. Chỉ chấp nhận: {', '.join(self.supported_formats)}"

        return True, "OK"

    def extract_text_from_pdf(self, file_path: str) -> str:
        """Trích xuất text từ PDF"""
        try:
            text = ""
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
            return text.strip()
        except Exception as e:
            print(f"Error reading PDF: {str(e)}")
            return ""

    def extract_text_from_docx(self, file_path: str) -> str:
        """Trích xuất text từ DOCX"""
        try:
            doc = Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text.strip()
        except Exception as e:
            print(f"Error reading DOCX: {str(e)}")
            return ""

    def extract_text_from_txt(self, file_path: str) -> str:
        """Trích xuất text từ TXT"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read().strip()
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='latin-1') as file:
                    return file.read().strip()
            except Exception as e:
                print(f"Error reading TXT: {str(e)}")
                return ""
        except Exception as e:
            print(f"Error reading TXT: {str(e)}")
            return ""

    def extract_text_from_image(self, file_path: str) -> str:
        """Trích xuất text từ ảnh (sử dụng Gemini Vision)"""
        try:
            # Sử dụng Gemini Vision để đọc text từ ảnh
            prompt = """
            Hãy đọc và trích xuất toàn bộ text từ CV trong ảnh này.
            Trả về text đầy đủ, bao gồm:
            - Thông tin cá nhân
            - Kinh nghiệm làm việc
            - Học vấn
            - Kỹ năng
            - Các thông tin khác
            
            Chỉ trả về text, không thêm giải thích.
            """

            # Đọc ảnh và gửi cho Gemini
            with open(file_path, 'rb') as image_file:
                image_data = image_file.read()

            # Tạm thời return empty string vì cần implement Gemini Vision
            return ""
        except Exception as e:
            print(f"Error reading image: {str(e)}")
            return ""

    def extract_text_from_file(self, file_path: str) -> str:
        """Trích xuất text từ file dựa trên định dạng"""
        file_ext = Path(file_path).suffix.lower()

        if file_ext == '.pdf':
            return self.extract_text_from_pdf(file_path)
        elif file_ext == '.docx':
            return self.extract_text_from_docx(file_path)
        elif file_ext == '.txt':
            return self.extract_text_from_txt(file_path)
        elif file_ext in ['.jpg', '.jpeg', '.png']:
            return self.extract_text_from_image(file_path)
        else:
            return ""

    def analyze_cv_content(self, cv_text: str) -> Dict:
        """Phân tích nội dung CV bằng AI"""
        if not cv_text.strip():
            return {
                "error": "Không thể đọc được nội dung CV",
                "skills": [],
                "experience": [],
                "education": [],
                "summary": ""
            }

        # Tạo prompt để phân tích CV
        prompt = f"""
        Hãy phân tích CV sau và trả về thông tin dưới dạng JSON với cấu trúc:
        {{
            "personal_info": {{
                "name": "Tên ứng viên",
                "email": "Email",
                "phone": "Số điện thoại",
                "location": "Địa chỉ"
            }},
            "skills": ["skill1", "skill2", "skill3"],
            "experience": [
                {{
                    "position": "Vị trí",
                    "company": "Công ty",
                    "duration": "Thời gian",
                    "description": "Mô tả công việc"
                }}
            ],
            "education": [
                {{
                    "degree": "Bằng cấp",
                    "school": "Trường",
                    "year": "Năm tốt nghiệp"
                }}
            ],
            "summary": "Tóm tắt ngắn gọn về ứng viên",
            "years_of_experience": số năm kinh nghiệm,
            "key_strengths": ["điểm mạnh 1", "điểm mạnh 2"]
        }}
        
        CV Content:
        {cv_text}
        
        Chỉ trả về JSON, không thêm text khác.
        """

        try:
            response = self.gemini.generate_response(prompt)
            # Parse JSON response
            cv_data = json.loads(response)
            return cv_data
        except json.JSONDecodeError:
            # Fallback: extract basic info using regex
            return self.extract_basic_info(cv_text)
        except Exception as e:
            print(f"Error analyzing CV: {str(e)}")
            return self.extract_basic_info(cv_text)

    def extract_basic_info(self, cv_text: str) -> Dict:
        """Trích xuất thông tin cơ bản bằng regex (fallback)"""
        # Extract email
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, cv_text)

        # Extract phone
        phone_pattern = r'(\+84|0)[0-9]{9,10}'
        phones = re.findall(phone_pattern, cv_text)

        # Extract skills (common programming languages and technologies)
        skill_keywords = [
            'Python', 'Java', 'JavaScript', 'React', 'Node.js', 'PHP', 'C++', 'C#',
            'SQL', 'MySQL', 'PostgreSQL', 'MongoDB', 'Docker', 'Kubernetes',
            'AWS', 'Azure', 'GCP', 'Git', 'Linux', 'HTML', 'CSS', 'Vue.js', 'Angular'
        ]

        found_skills = []
        for skill in skill_keywords:
            if skill.lower() in cv_text.lower():
                found_skills.append(skill)

        return {
            "personal_info": {
                "name": "Không xác định",
                "email": emails[0] if emails else "",
                "phone": phones[0] if phones else "",
                "location": ""
            },
            "skills": found_skills,
            "experience": [],
            "education": [],
            "summary": "CV đã được upload và phân tích cơ bản",
            "years_of_experience": 0,
            "key_strengths": found_skills[:3]
        }

    def match_jobs(self, cv_data: Dict) -> List[Dict]:
        """Gợi ý công việc phù hợp dựa trên CV"""
        user_skills = [skill.lower() for skill in cv_data.get('skills', [])]
        experience_years = cv_data.get('years_of_experience', 0)

        job_matches = []

        for job_id, job_info in JOB_POSITIONS.items():
            # Tính điểm match dựa trên skills
            required_skills = [skill.lower()
                               for skill in job_info.get('required_skills', [])]
            skill_matches = len(set(user_skills) & set(required_skills))
            total_required = len(required_skills)

            skill_score = (skill_matches / total_required *
                           100) if total_required > 0 else 0

            # Tính điểm match dựa trên kinh nghiệm
            min_exp = job_info.get('min_experience', 0)
            max_exp = job_info.get('max_experience', 10)

            if experience_years >= min_exp and experience_years <= max_exp:
                exp_score = 100
            elif experience_years < min_exp:
                exp_score = max(0, 100 - (min_exp - experience_years) * 20)
            else:
                exp_score = max(0, 100 - (experience_years - max_exp) * 10)

            # Tổng điểm (70% skills, 30% experience)
            total_score = skill_score * 0.7 + exp_score * 0.3

            if total_score > 30:  # Chỉ hiển thị job có điểm > 30%
                job_matches.append({
                    "job_id": job_id,
                    "title": job_info['title'],
                    "company": job_info.get('company', 'FOIS ICT PRO'),
                    "location": job_info.get('location', 'Hà Nội/HCM'),
                    "salary": job_info.get('salary', 'Thỏa thuận'),
                    "match_score": round(total_score, 1),
                    "skill_matches": skill_matches,
                    "total_skills": total_required,
                    "matched_skills": list(set(user_skills) & set(required_skills)),
                    "missing_skills": list(set(required_skills) - set(user_skills)),
                    "description": job_info.get('description', ''),
                    "requirements": job_info.get('requirements', [])
                })

        # Sắp xếp theo điểm match
        job_matches.sort(key=lambda x: x['match_score'], reverse=True)

        return job_matches[:5]  # Top 5 matches

    def generate_recommendations(self, cv_data: Dict, job_matches: List[Dict]) -> str:
        """Tạo gợi ý cải thiện CV và career path"""
        if not job_matches:
            return "Hiện tại chưa có vị trí phù hợp. Hãy cập nhật thêm kỹ năng và kinh nghiệm."

        best_match = job_matches[0]
        missing_skills = best_match.get('missing_skills', [])

        recommendations = []

        if missing_skills:
            recommendations.append(
                f"🎯 Để phù hợp hơn với vị trí '{best_match['title']}', bạn nên học thêm: {', '.join(missing_skills[:3])}")

        if cv_data.get('years_of_experience', 0) < 2:
            recommendations.append(
                "💼 Tích lũy thêm kinh nghiệm thực tế qua các dự án cá nhân hoặc freelance")

        if len(cv_data.get('skills', [])) < 5:
            recommendations.append(
                "🔧 Mở rộng bộ kỹ năng với các công nghệ hot như AI, Cloud, DevOps")

        recommendations.append(
            "📝 Cập nhật CV thường xuyên với các dự án và thành tích mới")

        return "\n".join(recommendations)

    def process_cv_file(self, file_path: str) -> Dict:
        """Xử lý file CV hoàn chỉnh"""
        try:
            # Check if CV libraries are available
            if not self._check_cv_libraries():
                return {
                    "success": False,
                    "error": "CV processing libraries not installed. Please install: PyPDF2 python-docx Pillow python-magic"
                }

            # Extract text
            cv_text = self.extract_text_from_file(file_path)

            if not cv_text.strip():
                return {
                    "success": False,
                    "error": "Không thể đọc được nội dung CV. Vui lòng kiểm tra định dạng file."
                }

            # Analyze CV
            cv_data = self.analyze_cv_content(cv_text)

            # Find matching jobs
            job_matches = self.match_jobs(cv_data)

            # Generate recommendations
            recommendations = self.generate_recommendations(
                cv_data, job_matches)

            return {
                "success": True,
                "cv_data": cv_data,
                "job_matches": job_matches,
                "recommendations": recommendations,
                "total_jobs_found": len(job_matches)
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Lỗi xử lý CV: {str(e)}"
            }

    def save_uploaded_file(self, file_data, filename: str) -> str:
        """Lưu file upload vào thư mục tạm"""
        upload_dir = Path("uploads")
        upload_dir.mkdir(exist_ok=True)

        file_path = upload_dir / filename

        with open(file_path, 'wb') as f:
            f.write(file_data)

        return str(file_path)
