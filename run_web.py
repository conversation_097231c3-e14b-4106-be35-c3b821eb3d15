#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script để chạy FOIS Chatbot Web Interface
"""

import os
import sys
from web_app import app
from config import COMPANY_INFO


def main():
    """Chạy web application"""
    print("🚀 FOIS CHATBOT WEB INTERFACE")
    print("=" * 50)
    print(f"🏢 Company: {COMPANY_INFO['FULL_NAME']}")
    print(f"📅 Established: {COMPANY_INFO['ESTABLISHED']}")
    print(
        f"🌍 Locations: {', '.join(COMPANY_INFO['LOCATIONS']['JAPAN'])} & {', '.join(COMPANY_INFO['LOCATIONS']['VIETNAM'])}")
    print(f"🎯 Mission: {COMPANY_INFO['MISSION']}")
    print("=" * 50)

    # Check environment
    print("🔍 Checking environment...")

    # Check if .env file exists
    if not os.path.exists('.env'):
        print("⚠️  Warning: .env file not found")
        print("   Please create .env file with GEMINI_API_KEY")

    # Check Gemini AI
    try:
        from gemini_ai import GeminiAI
        ai = GeminiAI()
        if ai.is_available():
            print("✅ Gemini AI: Available")
        else:
            print("❌ Gemini AI: Not available")
            print("   Please check your GEMINI_API_KEY in .env file")
    except Exception as e:
        print(f"❌ Gemini AI Error: {str(e)}")

    # Check sample data
    try:
        from sample_data import SAMPLE_JOBS, SALARY_RANGES
        print(
            f"✅ Sample Data: {len(SAMPLE_JOBS)} jobs, {len(SALARY_RANGES)} salary ranges")
    except Exception as e:
        print(f"❌ Sample Data Error: {str(e)}")

    print("=" * 50)
    print("🌐 Starting web server...")
    print("📱 Access the chatbot at: http://localhost:5000")
    print("🛑 Press Ctrl+C to stop")
    print("=" * 50)

    try:
        # Get port from environment (for Replit/Heroku compatibility)
        port = int(os.environ.get('PORT', 5000))
        host = '0.0.0.0'

        # Check if running on Replit
        is_replit = 'REPL_SLUG' in os.environ
        debug_mode = not is_replit  # Disable debug on Replit

        if is_replit:
            print(
                f"🌐 Replit URL: https://{os.environ.get('REPL_SLUG')}.{os.environ.get('REPL_OWNER')}.repl.co")
            print(
                f"🎨 Modern UI: https://{os.environ.get('REPL_SLUG')}.{os.environ.get('REPL_OWNER')}.repl.co/modern")
        else:
            print(f"🌐 Local URL: http://localhost:{port}")
            print(f"🎨 Modern UI: http://localhost:{port}/modern")

        # Run Flask app
        app.run(
            host=host,
            port=port,
            debug=debug_mode,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
