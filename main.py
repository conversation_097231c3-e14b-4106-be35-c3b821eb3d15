import smtplib
import imaplib
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import time
import re
from datetime import datetime
import PyPDF2
import docx
import tempfile
import base64
import io
import threading
import select
import socket

# Import c<PERSON>u hình
from config import (
    SMTP_SETTINGS, IMAP_SETTINGS, GEMINI_API_KEY,
    CHATBOT_SETTINGS, COMPANY_INFO,
    KEYWORD_RESPONSES, DEFAULT_RESPONSE, validate_config
)

# Import modules
from gemini_ai import GeminiAI
from intent_detector import IntentDetector, IntentResult, IntentType
from context_manager import get_context_manager
from new_response_generator import NewResponseGenerator


class EmailChatbot:
    def __init__(self):
        self.smtp_server = SMTP_SETTINGS["SMTP_SERVER"]
        self.smtp_port = SMTP_SETTINGS["SMTP_PORT"]
        self.sender_email = SMTP_SETTINGS["SENDER_MAIL"]
        self.password = SMTP_SETTINGS["PASSWORD"]

        self.imap_server = IMAP_SETTINGS["IMAP_SERVER"]
        self.imap_port = IMAP_SETTINGS["IMAP_PORT"]

        # Lưu trữ các email đã xử lý để tránh trùng lặp
        self.processed_emails = set()

        # Khởi tạo AI và Intent system
        self.gemini_ai = GeminiAI()
        self.intent_detector = IntentDetector(
            self.gemini_ai)  # Truyền AI vào detector
        self.context_manager = get_context_manager()
        # Sử dụng email_mode=True cho email chatbot
        self.response_generator = NewResponseGenerator(
            self.gemini_ai, email_mode=True)

        # Cờ để bật/tắt AI (từ config)
        self.use_ai = CHATBOT_SETTINGS['USE_AI']

    def toggle_ai(self, enable=None):
        """Bật/tắt chế độ AI"""
        if enable is None:
            self.use_ai = not self.use_ai
        else:
            self.use_ai = enable

        status = "BẬT" if self.use_ai else "TẮT"
        print(f"🤖 Chế độ AI: {status}")
        return self.use_ai

    def send_email(self, to_email, subject, message, is_html=False):
        """Gửi email phản hồi với hỗ trợ HTML"""
        try:
            # Tạo message object
            msg = MIMEMultipart('alternative')
            msg['From'] = self.sender_email
            msg['To'] = to_email
            msg['Subject'] = subject

            # Xác định content type
            if is_html:
                # Kiểm tra xem message có chứa HTML tags không
                if '<' in message and '>' in message:
                    # Gửi cả plain text và HTML version
                    # Tạo plain text version bằng cách loại bỏ HTML tags
                    import re
                    plain_text = re.sub('<[^<]+?>', '', message)
                    plain_text = plain_text.replace(
                        '&nbsp;', ' ').replace('&amp;', '&')

                    # Thêm cả hai version
                    msg.attach(MIMEText(plain_text, 'plain', 'utf-8'))
                    msg.attach(MIMEText(message, 'html', 'utf-8'))
                    print("📧 Gửi email HTML với fallback plain text")
                else:
                    # Nếu không có HTML tags, gửi như plain text
                    msg.attach(MIMEText(message, 'plain', 'utf-8'))
                    print("📧 Gửi email plain text (không có HTML tags)")
            else:
                # Gửi plain text
                msg.attach(MIMEText(message, 'plain', 'utf-8'))
                print("📧 Gửi email plain text")

            # Kết nối SMTP server
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()  # Bật mã hóa TLS
            server.login(self.sender_email, self.password)

            # Gửi email
            text = msg.as_string()
            server.sendmail(self.sender_email, to_email, text)
            server.quit()

            print(f"✅ Đã gửi email thành công đến {to_email}")
            return True

        except Exception as e:
            print(f"❌ Lỗi khi gửi email: {str(e)}")
            return False

    def read_emails(self):
        """Đọc email từ hộp thư đến"""
        try:
            # Kết nối IMAP server
            mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            mail.login(self.sender_email, self.password)
            mail.select('inbox')

            # Tìm email chưa đọc
            status, messages = mail.search(None, 'UNSEEN')
            email_ids = messages[0].split()

            new_emails = []

            for email_id in email_ids:
                # Lấy email
                status, msg_data = mail.fetch(email_id, '(RFC822)')

                for response_part in msg_data:
                    if isinstance(response_part, tuple):
                        # Parse email
                        msg = email.message_from_bytes(response_part[1])

                        # Lấy thông tin email
                        subject = msg['subject']
                        from_email = msg['from']

                        # Lấy nội dung email
                        body = self.get_email_body(msg)

                        # Extract attachments and CV content
                        attachments, cv_content = self.extract_attachments(msg)

                        # Combine email body with CV content
                        full_content = body
                        if cv_content:
                            full_content += cv_content
                            print(
                                f"📄 Đã thêm nội dung CV vào email ({len(cv_content)} ký tự)")

                        email_info = {
                            'id': email_id.decode(),
                            'from': from_email,
                            'subject': subject,
                            'body': full_content,
                            'attachments': attachments,
                            'has_cv': bool(cv_content)
                        }

                        new_emails.append(email_info)

            mail.close()
            mail.logout()

            return new_emails

        except Exception as e:
            print(f"❌ Lỗi khi đọc email: {str(e)}")
            return []

    def get_email_body(self, msg):
        """Lấy nội dung email"""
        body = ""

        if msg.is_multipart():
            for part in msg.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))

                if content_type == "text/plain" and "attachment" not in content_disposition:
                    try:
                        body = part.get_payload(decode=True).decode('utf-8')
                    except:
                        try:
                            body = part.get_payload(
                                decode=True).decode('latin-1')
                        except:
                            body = str(part.get_payload())
                    break
        else:
            try:
                body = msg.get_payload(decode=True).decode('utf-8')
            except:
                try:
                    body = msg.get_payload(decode=True).decode('latin-1')
                except:
                    body = str(msg.get_payload())

        return body.strip()

    def extract_text_from_pdf(self, pdf_data):
        """Trích xuất text từ file PDF"""
        try:
            pdf_file = io.BytesIO(pdf_data)
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            text = ""

            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"

            return text.strip()
        except Exception as e:
            print(f"❌ Lỗi khi đọc PDF: {str(e)}")
            return ""

    def extract_text_from_docx(self, docx_data):
        """Trích xuất text từ file DOCX"""
        try:
            docx_file = io.BytesIO(docx_data)
            doc = docx.Document(docx_file)
            text = ""

            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"

            return text.strip()
        except Exception as e:
            print(f"❌ Lỗi khi đọc DOCX: {str(e)}")
            return ""

    def extract_attachments(self, msg):
        """Trích xuất và phân tích file đính kèm"""
        attachments = []
        cv_content = ""

        for part in msg.walk():
            if part.get_content_disposition() == 'attachment':
                filename = part.get_filename()
                if filename:
                    print(f"📎 Tìm thấy file đính kèm: {filename}")

                    # Decode filename nếu cần
                    if filename.startswith('=?'):
                        decoded_filename = email.header.decode_header(filename)[
                            0]
                        if decoded_filename[1]:
                            filename = decoded_filename[0].decode(
                                decoded_filename[1])
                        else:
                            filename = decoded_filename[0]

                    # Lấy dữ liệu file
                    file_data = part.get_payload(decode=True)
                    file_size = len(file_data)

                    print(f"📄 File: {filename} ({file_size} bytes)")

                    # Kiểm tra xem có phải CV không
                    filename_lower = filename.lower()
                    is_cv = any(keyword in filename_lower for keyword in [
                                'cv', 'resume', 'curriculum'])

                    # Trích xuất text từ file CV
                    if is_cv or filename_lower.endswith(('.pdf', '.docx', '.doc')):
                        print(f"🔍 Phân tích file CV: {filename}")

                        if filename_lower.endswith('.pdf'):
                            cv_text = self.extract_text_from_pdf(file_data)
                        elif filename_lower.endswith('.docx'):
                            cv_text = self.extract_text_from_docx(file_data)
                        else:
                            print(
                                f"⚠️ Định dạng file không được hỗ trợ: {filename}")
                            cv_text = ""

                        if cv_text:
                            cv_content += f"\n\n📄 **Nội dung CV từ file {filename}:**\n{cv_text}"
                            print(
                                f"✅ Đã trích xuất {len(cv_text)} ký tự từ {filename}")
                        else:
                            print(
                                f"⚠️ Không thể trích xuất text từ {filename}")

                    attachments.append({
                        'filename': filename,
                        'size': file_size,
                        'is_cv': is_cv
                    })

        return attachments, cv_content

    def generate_response(self, email_content, sender_email, subject=""):
        """Tạo phản hồi tự động dựa trên nội dung email với NewResponseGenerator"""

        # Sử dụng email làm user_id (có thể cải thiện sau)
        user_id = sender_email

        print(f"\n📧 DEBUG: Generating response for user: {user_id}")
        print(f"📝 DEBUG: Email content: {email_content[:100]}...")
        print(f"🤖 DEBUG: AI enabled: {self.use_ai}")

        # Tạo phản hồi dựa trên AI
        if self.use_ai:
            try:
                # Lấy lịch sử hội thoại từ context manager
                conversation_history = []
                if self.context_manager.has_context(user_id):
                    context = self.context_manager.get_context(user_id)
                    print(
                        f"📚 DEBUG: Found {len(context.conversation_turns)} previous turns")
                    # Chuyển đổi context thành format phù hợp với NewResponseGenerator
                    for i, turn in enumerate(context.conversation_turns):
                        conversation_history.append({
                            'type': 'user',
                            'message': turn.user_input
                        })
                        conversation_history.append({
                            'type': 'bot',
                            'message': turn.bot_response
                        })
                        print(
                            f"   Turn {i+1}: User: {turn.user_input[:50]}...")
                        print(
                            f"   Turn {i+1}: Bot: {turn.bot_response[:50]}...")
                else:
                    print("📚 DEBUG: No previous conversation history found")

                # Gọi NewResponseGenerator
                response_data = self.response_generator.generate_response(
                    user_id, email_content, conversation_history
                )

                print("🤖 Sử dụng phản hồi từ NewResponseGenerator + AI")
                print(
                    f"🎯 User intent: {response_data.get('user_intent', 'unknown')}")
                print(f"😊 Tone: {response_data.get('tone', 'unknown')}")
                print(
                    f"🔄 Needs callback: {response_data.get('needs_callback', False)}")

                # Xử lý needs_callback cho email mode
                if response_data.get('needs_callback', False):
                    print("📧 Email mode: Bỏ qua callback, trả về response đầy đủ")
                    # Trong email mode, chúng ta không cần thinking message
                    # Trả về response đầy đủ ngay lập tức
                    pass

                # Tạo IntentResult giả từ response data để lưu vào context
                try:
                    # Map user_intent từ NewResponseGenerator sang IntentType
                    intent_mapping = {
                        'greeting': IntentType.GREETINGS,
                        'ask_company_info': IntentType.ASK_COMPANY_INFO,
                        'search_jobs': IntentType.ASK_JOB_OPPORTUNITIES,
                        'filter_jobs': IntentType.ASK_JOB_OPPORTUNITIES,
                        'apply_job': IntentType.ASK_JOB_DETAILS,
                        'salary_query': IntentType.SALARY_EXPECTATION,
                        'smalltalk': IntentType.OTHER,
                        'off_topic': IntentType.OTHER,
                        'thank_you': IntentType.OTHER,
                        'complaint': IntentType.OTHER,
                        'farewell': IntentType.OTHER
                    }

                    user_intent = response_data.get('user_intent', 'other')
                    intent_type = intent_mapping.get(
                        user_intent, IntentType.OTHER)

                    fake_intent_result = IntentResult(
                        intent=intent_type,
                        confidence=0.8,  # Confidence giả
                        entities={},  # Entities rỗng
                        context_needed=False
                    )

                    # Lưu vào context manager
                    self.context_manager.add_conversation_turn(
                        user_id,
                        email_content,
                        fake_intent_result,
                        response_data.get('message', '')
                    )
                except Exception as context_error:
                    print(f"⚠️ Lỗi khi lưu context: {str(context_error)}")
                    # Không lưu context nếu có lỗi, nhưng vẫn trả về response

                # Trả về message từ response
                return response_data.get('message', 'Xin lỗi, tôi không thể tạo phản hồi lúc này.')

            except Exception as e:
                print(
                    f"⚠️ NewResponseGenerator lỗi, chuyển sang phản hồi cơ bản: {str(e)}")
                import traceback
                print("🔍 DEBUG: Full traceback:")
                traceback.print_exc()

        # Phản hồi cơ bản dựa trên từ khóa (fallback)
        print("📝 Sử dụng phản hồi cơ bản")
        return self._generate_keyword_response(email_content, sender_email)

    def _generate_keyword_response(self, email_content, sender_email):
        """Tạo phản hồi dựa trên từ khóa (phương pháp cũ)"""
        # Chuyển nội dung về chữ thường để dễ xử lý
        content_lower = email_content.lower()

        # Tìm phản hồi phù hợp từ config
        for keywords, response_data in KEYWORD_RESPONSES.items():
            if re.search(keywords, content_lower):
                response = response_data.get('vi', response_data)
                # Thay thế timestamp nếu có
                if '{timestamp}' in response:
                    response = response.replace(
                        '{timestamp}', datetime.now().strftime('%Y%m%d%H%M%S'))
                return response

        # Phản hồi mặc định từ config
        return DEFAULT_RESPONSE.get('vi', DEFAULT_RESPONSE)

    def process_emails(self):
        """Xử lý email và gửi phản hồi tự động"""
        print("🔍 Đang kiểm tra email mới...")

        # Đọc email mới
        new_emails = self.read_emails()

        if not new_emails:
            print("📭 Không có email mới")
            return

        print(f"📧 Tìm thấy {len(new_emails)} email mới")

        for email_info in new_emails:
            email_id = email_info['id']

            # Kiểm tra email đã xử lý chưa
            if email_id in self.processed_emails:
                continue

            from_email = email_info['from']
            subject = email_info['subject'] or "No Subject"
            body = email_info['body']

            print(f"\n📨 Xử lý email từ: {from_email}")
            print(f"📋 Tiêu đề: {subject}")

            # Hiển thị thông tin về attachments
            if email_info.get('attachments'):
                print(
                    f"📎 File đính kèm: {len(email_info['attachments'])} file(s)")
                for attachment in email_info['attachments']:
                    cv_indicator = " (CV)" if attachment['is_cv'] else ""
                    print(
                        f"   - {attachment['filename']} ({attachment['size']} bytes){cv_indicator}")

            if email_info.get('has_cv'):
                print("🎯 Email có chứa CV - sẽ phân tích nội dung CV")

            print(f"📝 Nội dung email: {body[:200]}...")
            print(f"📏 Độ dài nội dung: {len(body)} ký tự")

            # Tạo phản hồi
            response = self.generate_response(body, from_email, subject)

            # Tạo tiêu đề phản hồi
            reply_subject = f"Re: {subject}" if not subject.startswith(
                "Re:") else subject

            # Gửi phản hồi với HTML support
            # Sử dụng HTML mode vì NewResponseGenerator trả về HTML format
            if self.send_email(from_email, reply_subject, response, is_html=True):
                self.processed_emails.add(email_id)
                print(f"✅ Đã phản hồi email từ {from_email}")
            else:
                print(f"❌ Không thể phản hồi email từ {from_email}")

    def run_chatbot(self, check_interval=60):
        """Chạy chatbot liên tục"""
        print("🤖 Email Chatbot đã khởi động!")
        print(f"📧 Email: {self.sender_email}")
        print(f"⏰ Kiểm tra email mỗi {check_interval} giây")
        print("=" * 50)

        try:
            while True:
                self.process_emails()
                print(
                    f"\n⏳ Chờ {check_interval} giây trước khi kiểm tra lại...")
                time.sleep(check_interval)

        except KeyboardInterrupt:
            print("\n🛑 Chatbot đã dừng!")
        except Exception as e:
            print(f"\n❌ Lỗi: {str(e)}")


def main():
    """Hàm chính"""
    print("🚀 Khởi động Email Chatbot với AI...")
    print("=" * 50)

    # Kiểm tra cấu hình
    if not validate_config():
        print("\n❌ Cấu hình không đầy đủ!")
        print("💡 Hướng dẫn:")
        print("1. Copy file .env.example thành .env")
        print("2. Cập nhật các giá trị trong file .env")
        print("3. Chạy lại chatbot")
        return

    chatbot = EmailChatbot()

    # Hiển thị thông tin cấu hình
    print(f"📧 Email: {chatbot.sender_email}")
    print(f"🤖 AI Mode: {'BẬT' if chatbot.use_ai else 'TẮT'}")
    print(
        f"🔑 Gemini API: {'Đã cấu hình' if GEMINI_API_KEY else 'Chưa cấu hình'}")
    print("=" * 50)

    # Test AI (nếu được bật)
    if chatbot.use_ai:
        print("\n🤖 Test NewResponseGenerator (Email Mode)...")
        try:
            test_response_data = chatbot.response_generator.generate_response(
                CHATBOT_SETTINGS['TEST_EMAIL'],
                "Xin chào, tôi muốn biết thêm về dịch vụ của công ty",
                []  # Không có lịch sử hội thoại cho test
            )
            print("✅ NewResponseGenerator (Email Mode) hoạt động bình thường")
            print(
                f"🎯 Intent: {test_response_data.get('user_intent', 'unknown')}")
            print(f"😊 Tone: {test_response_data.get('tone', 'unknown')}")
            print(f"📧 Email Mode: {chatbot.response_generator.email_mode}")
            print(
                f"📝 Mẫu phản hồi AI:\n{test_response_data.get('message', '')[:150]}...")
        except Exception as e:
            print(f"⚠️ NewResponseGenerator có vấn đề: {str(e)}")
            print("📝 Sẽ sử dụng phản hồi cơ bản")

    print("\n" + "=" * 50)
    print("💡 Gợi ý: Nhấn Ctrl+C để dừng chatbot")
    print("=" * 50)

    # Chạy chatbot
    chatbot.run_chatbot(check_interval=CHATBOT_SETTINGS['CHECK_INTERVAL'])


if __name__ == "__main__":
    main()
