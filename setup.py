#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Setup script cho Email Chatbot với AI
"""

import os
import sys
import subprocess
import shutil

def print_step(step, message):
    """In bước thực hiện"""
    print(f"\n{'='*60}")
    print(f"BƯỚC {step}: {message}")
    print('='*60)

def run_command(command, description):
    """Chạy command và kiểm tra kết quả"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} thành công!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} thất bại!")
        print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Kiểm tra phiên bản Python"""
    print_step(1, "Kiểm tra phiên bản Python")
    
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Cần Python 3.8 trở lên!")
        return False
    
    print("✅ Phiên bản Python phù hợp!")
    return True

def setup_virtual_environment():
    """Tạo và kích hoạt virtual environment"""
    print_step(2, "Thiết lập Virtual Environment")
    
    if os.path.exists('.venv'):
        print("📁 Virtual environment đã tồn tại")
        return True
    
    if not run_command("python -m venv .venv", "Tạo virtual environment"):
        return False
    
    print("✅ Virtual environment đã được tạo!")
    return True

def install_dependencies():
    """Cài đặt dependencies"""
    print_step(3, "Cài đặt Dependencies")
    
    # Determine activation command based on OS
    if os.name == 'nt':  # Windows
        activate_cmd = ".venv\\Scripts\\activate"
    else:  # Unix/Linux/MacOS
        activate_cmd = "source .venv/bin/activate"
    
    command = f"{activate_cmd} && pip install --upgrade pip && pip install -r requirements.txt"
    
    return run_command(command, "Cài đặt dependencies")

def setup_env_file():
    """Thiết lập file .env"""
    print_step(4, "Thiết lập file .env")
    
    if os.path.exists('.env'):
        print("📁 File .env đã tồn tại")
        response = input("Bạn có muốn ghi đè không? (y/N): ")
        if response.lower() != 'y':
            print("⏭️ Bỏ qua thiết lập .env")
            return True
    
    if not os.path.exists('.env.example'):
        print("❌ Không tìm thấy file .env.example!")
        return False
    
    try:
        shutil.copy('.env.example', '.env')
        print("✅ Đã tạo file .env từ template!")
        print("\n📝 Bạn cần cập nhật các giá trị sau trong file .env:")
        print("   - GEMINI_API_KEY")
        print("   - SMTP_SENDER_MAIL")
        print("   - SMTP_PASSWORD")
        print("   - IMAP_EMAIL")
        print("   - IMAP_PASSWORD")
        return True
    except Exception as e:
        print(f"❌ Lỗi khi tạo file .env: {str(e)}")
        return False

def test_installation():
    """Test cài đặt"""
    print_step(5, "Test cài đặt")
    
    # Determine activation command based on OS
    if os.name == 'nt':  # Windows
        activate_cmd = ".venv\\Scripts\\activate"
    else:  # Unix/Linux/MacOS
        activate_cmd = "source .venv/bin/activate"
    
    command = f"{activate_cmd} && python -c \"from config import validate_config; print('Import thành công!')\""
    
    if run_command(command, "Test import modules"):
        print("✅ Cài đặt hoàn tất!")
        return True
    else:
        print("❌ Có lỗi trong quá trình cài đặt!")
        return False

def print_next_steps():
    """In hướng dẫn bước tiếp theo"""
    print("\n" + "="*60)
    print("🎉 SETUP HOÀN TẤT!")
    print("="*60)
    print("\n📋 Các bước tiếp theo:")
    print("1. Cập nhật file .env với thông tin thực tế:")
    print("   - Gemini API Key từ https://makersuite.google.com/")
    print("   - Thông tin email Gmail và App Password")
    print("\n2. Test cấu hình:")
    print("   python run_chatbot.py --test-ai")
    print("\n3. Chạy chatbot:")
    print("   python main.py")
    print("   hoặc")
    print("   python run_chatbot.py")
    print("\n4. Xem demo:")
    print("   python demo.py")
    print("\n💡 Để xem tất cả tùy chọn:")
    print("   python run_chatbot.py --help")

def main():
    """Hàm chính"""
    print("🚀 SETUP EMAIL CHATBOT VỚI AI")
    print("="*60)
    print("Script này sẽ thiết lập môi trường cho Email Chatbot")
    
    try:
        # Kiểm tra Python version
        if not check_python_version():
            return False
        
        # Thiết lập virtual environment
        if not setup_virtual_environment():
            return False
        
        # Cài đặt dependencies
        if not install_dependencies():
            return False
        
        # Thiết lập .env file
        if not setup_env_file():
            return False
        
        # Test cài đặt
        if not test_installation():
            return False
        
        # In hướng dẫn bước tiếp theo
        print_next_steps()
        return True
        
    except KeyboardInterrupt:
        print("\n\n🛑 Setup bị hủy bởi người dùng!")
        return False
    except Exception as e:
        print(f"\n\n❌ Lỗi không mong muốn: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
