#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vector-Enhanced Response Generator
Enhanced version of NewResponseGenerator that uses vector-based intent classification
"""

import sys
import os
from typing import Dict, List, Tuple
from datetime import datetime

# Add parent directory to path to import existing modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from new_response_generator import NewResponseGenerator
from gemini_ai import GeminiAI
from vector_intent_detector import VectorIntentDetector
from intent_integration import IntentIntegration
from bot_personality import BOT_PERSONALITY
from sample_data import *
from google import genai
from google.genai import types


class NewResponseGeneratorVector(NewResponseGenerator):
    """
    Vector-enhanced response generator that uses vector-based intent classification
    for more accurate and context-aware responses
    """
    
    def __init__(self, gemini_ai: GeminiAI):
        """Initialize the vector-enhanced response generator"""
        super().__init__(gemini_ai)
        
        # Initialize vector components
        self.vector_detector = VectorIntentDetector()
        self.intent_integration = IntentIntegration()
        
        print("✅ Vector-Enhanced Response Generator initialized")
    
    def generate_response_with_vector_intent(self, user_id: str, user_input: str, 
                                           conversation_history: List[Dict],
                                           vector_intent: str, confidence: float,
                                           similarities: Dict[str, float]) -> Dict:
        """
        Generate response using vector-detected intent information
        
        Args:
            user_id: User identifier
            user_input: User's message
            conversation_history: Previous conversation context
            vector_intent: Intent detected by vector analysis
            confidence: Confidence score of the detected intent
            similarities: All intent similarity scores
            
        Returns:
            Dict containing response and metadata
        """
        print(f"🎨 Generating vector-enhanced response...")
        print(f"   Vector Intent: {vector_intent} (confidence: {confidence:.4f})")
        
        try:
            # Map vector intent to response generator intent
            mapped_intent = self.intent_integration.intent_mapping.get(vector_intent, vector_intent)
            print(f"   Mapped Intent: {mapped_intent}")
            
            # Prepare enhanced context with vector information
            enhanced_context = self._prepare_vector_enhanced_context(
                user_input, conversation_history, vector_intent, confidence, similarities
            )
            
            # Generate response based on mapped intent
            if mapped_intent in ['search_jobs', 'filter_jobs', 'job_it_trending']:
                response_data = self._generate_job_related_response(
                    user_id, user_input, conversation_history, mapped_intent, enhanced_context
                )
            elif mapped_intent in ['cv_feedback', 'upload_resume', 'update_resume']:
                response_data = self._generate_cv_related_response(
                    user_id, user_input, conversation_history, mapped_intent, enhanced_context
                )
            elif mapped_intent == 'salary_query':
                response_data = self._generate_salary_response(
                    user_id, user_input, conversation_history, enhanced_context
                )
            elif mapped_intent == 'ask_company_info':
                response_data = self._generate_company_info_response(
                    user_id, user_input, conversation_history, enhanced_context
                )
            else:
                # Use general response generation with vector enhancement
                response_data = self._generate_general_vector_response(
                    user_id, user_input, conversation_history, mapped_intent, enhanced_context
                )
            
            # Add vector-specific metadata
            response_data.update({
                'vector_intent': vector_intent,
                'vector_confidence': confidence,
                'mapped_intent': mapped_intent,
                'intent_similarities': similarities,
                'vector_enhanced': True
            })
            
            return response_data
            
        except Exception as e:
            print(f"❌ Error in vector response generation: {e}")
            # Fallback to standard response generation
            return self.generate_response(user_id, user_input, conversation_history)
    
    def _prepare_vector_enhanced_context(self, user_input: str, conversation_history: List[Dict],
                                       vector_intent: str, confidence: float, 
                                       similarities: Dict[str, float]) -> Dict:
        """Prepare enhanced context with vector information"""
        
        # Get top 3 similar intents for context
        top_intents = sorted(similarities.items(), key=lambda x: x[1], reverse=True)[:3]
        
        return {
            'user_input': user_input,
            'conversation_history': conversation_history,
            'vector_analysis': {
                'primary_intent': vector_intent,
                'confidence': confidence,
                'top_similar_intents': top_intents,
                'intent_certainty': 'high' if confidence > 0.7 else 'medium' if confidence > 0.4 else 'low'
            },
            'context_enrichment': self._get_context_enrichment(vector_intent, confidence)
        }
    
    def _get_context_enrichment(self, vector_intent: str, confidence: float) -> Dict:
        """Get additional context based on vector intent analysis"""
        enrichment = {
            'response_style': 'standard',
            'additional_data': {},
            'suggested_followups': []
        }
        
        # Customize based on intent and confidence
        if confidence > 0.8:
            enrichment['response_style'] = 'confident'
        elif confidence < 0.4:
            enrichment['response_style'] = 'clarifying'
            enrichment['suggested_followups'] = [
                "Bạn có thể nói rõ hơn về điều bạn muốn tìm hiểu không?",
                "Tôi có thể giúp bạn tìm kiếm thông tin cụ thể nào?"
            ]
        
        # Add intent-specific enrichment
        if vector_intent in ['ASK_JOB_OPPORTUNITIES', 'ASK_JOB_DETAILS']:
            enrichment['additional_data'] = {
                'job_market_context': True,
                'salary_insights': True,
                'trending_skills': True
            }
        elif vector_intent == 'ASK_COMPANY_INFO':
            enrichment['additional_data'] = {
                'company_culture': True,
                'benefits': True,
                'career_growth': True
            }
        
        return enrichment
    
    def _generate_job_related_response(self, user_id: str, user_input: str, 
                                     conversation_history: List[Dict], intent: str,
                                     enhanced_context: Dict) -> Dict:
        """Generate job-related response with vector enhancement"""
        
        # Use the existing job trending response with vector context
        if intent == 'job_it_trending':
            response_data = self.generate_job_it_trending_response(
                user_id, user_input, conversation_history
            )
        else:
            # Use thinking message response for other job intents
            response_data = self.generate_thinking_message_response(
                user_id, user_input, conversation_history, intent
            )
        
        # Enhance with vector context
        confidence_level = enhanced_context['vector_analysis']['intent_certainty']
        if confidence_level == 'high':
            response_data['tone'] = 'confident'
            response_data['emotion'] = 'enthusiastic'
        elif confidence_level == 'low':
            # Add clarifying questions
            response_data['suggestion_answers'].extend([
                "Bạn đang tìm kiếm vị trí nào cụ thể?",
                "Bạn có kinh nghiệm bao nhiêu năm?",
                "Bạn quan tâm đến mức lương như thế nào?"
            ])
        
        return response_data
    
    def _generate_cv_related_response(self, user_id: str, user_input: str,
                                    conversation_history: List[Dict], intent: str,
                                    enhanced_context: Dict) -> Dict:
        """Generate CV-related response with vector enhancement"""
        
        response_data = self.generate_thinking_message_response(
            user_id, user_input, conversation_history, intent
        )
        
        # Add CV-specific enhancements based on vector confidence
        confidence = enhanced_context['vector_analysis']['confidence']
        if confidence > 0.7:
            response_data['contextual_followup']['cv_tips'] = [
                "Đảm bảo CV có định dạng rõ ràng và dễ đọc",
                "Highlight các kỹ năng phù hợp với vị trí ứng tuyển",
                "Thêm các dự án cụ thể bạn đã thực hiện"
            ]
        
        return response_data
    
    def _generate_salary_response(self, user_id: str, user_input: str,
                                conversation_history: List[Dict], enhanced_context: Dict) -> Dict:
        """Generate salary-related response with market data"""
        
        response_data = self.generate_thinking_message_response(
            user_id, user_input, conversation_history, 'salary_query'
        )
        
        # Add salary market insights
        response_data['contextual_followup']['salary_insights'] = {
            'market_trends': "Thị trường IT Việt Nam 2024-2025 có mức tăng trưởng mạnh",
            'hot_skills': ["AI/ML", "Cloud Computing", "DevOps", "Blockchain"],
            'salary_ranges': "Từ 15-50 triệu VND tùy theo kinh nghiệm và kỹ năng"
        }
        
        return response_data
    
    def _generate_company_info_response(self, user_id: str, user_input: str,
                                      conversation_history: List[Dict], enhanced_context: Dict) -> Dict:
        """Generate company information response"""
        
        response_data = self.generate_thinking_message_response(
            user_id, user_input, conversation_history, 'ask_company_info'
        )
        
        # Add company-specific enhancements
        response_data['contextual_followup']['company_highlights'] = {
            'culture': "Môi trường làm việc năng động và sáng tạo",
            'benefits': "Chế độ đãi ngộ cạnh tranh, cơ hội phát triển",
            'technology': "Sử dụng công nghệ hiện đại nhất"
        }
        
        return response_data
    
    def _generate_general_vector_response(self, user_id: str, user_input: str,
                                        conversation_history: List[Dict], intent: str,
                                        enhanced_context: Dict) -> Dict:
        """Generate general response with vector enhancement"""
        
        # Use standard response generation
        response_data = self.generate_response(user_id, user_input, conversation_history)
        
        # Apply vector-based enhancements
        confidence = enhanced_context['vector_analysis']['confidence']
        
        if confidence < 0.4:
            # Low confidence - add clarifying elements
            response_data['response_type'] = 'clarifying'
            response_data['tone'] = 'helpful'
            response_data['suggestion_answers'].extend([
                "Bạn có thể nói rõ hơn về điều bạn muốn biết không?",
                "Tôi có thể hỗ trợ bạn tìm hiểu về cơ hội việc làm IT",
                "Bạn có muốn biết về quy trình ứng tuyển không?"
            ])
        
        return response_data
    
    def detect_intent_with_vector_analysis(self, user_input: str) -> Tuple[str, float, Dict[str, float]]:
        """Detect intent using vector analysis"""
        try:
            return self.vector_detector.get_best_intent(user_input)
        except Exception as e:
            print(f"⚠️ Vector intent detection failed: {e}")
            return "OTHER", 0.0, {}
