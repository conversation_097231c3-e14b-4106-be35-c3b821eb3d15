#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Vector Chatbot System
Test the complete 6-step vector-based chatbot process and verify high similarity scores
"""

import sys
import os
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from vector_chatbot_router import Vector<PERSON><PERSON><PERSON>Router
from new_response_generator_vector import NewResponseGeneratorVector
from gemini_ai import GeminiA<PERSON>


def test_vector_chatbot_system():
    """Test the complete vector chatbot system"""
    print("🧪 Testing Vector Chatbot System")
    print("=" * 50)
    
    try:
        # Initialize components
        print("🚀 Initializing components...")
        router = VectorChatbotRouter()
        gemini_ai = GeminiAI()
        vector_gen = NewResponseGeneratorVector(gemini_ai)
        
        # Test cases with expected high similarity scores
        test_cases = [
            {
                "input": "Hello, I want to know about job opportunities",
                "expected_intent": "ASK_JOB_OPPORTUNITIES",
                "description": "Job inquiry greeting",
                "expected_confidence": 0.7
            },
            {
                "input": "What is your company about?",
                "expected_intent": "ASK_COMPANY_INFO",
                "description": "Company information request",
                "expected_confidence": 0.8
            },
            {
                "input": "I want to upload my CV for review",
                "expected_intent": "SHARE_PROFILE",
                "description": "CV upload request",
                "expected_confidence": 0.8
            },
            {
                "input": "What is the salary range for Python developers?",
                "expected_intent": "SALARY_EXPECTATION",
                "description": "Salary inquiry",
                "expected_confidence": 0.7
            },
            {
                "input": "Can you help me find remote jobs?",
                "expected_intent": "ASK_JOB_OPPORTUNITIES",
                "description": "Remote job search",
                "expected_confidence": 0.7
            },
            {
                "input": "Hi there!",
                "expected_intent": "GREETINGS",
                "description": "Simple greeting",
                "expected_confidence": 0.9
            },
            {
                "input": "I'm not interested in this position",
                "expected_intent": "NOT_INTERESTED",
                "description": "Rejection message",
                "expected_confidence": 0.8
            }
        ]
        
        print(f"\n📋 Running {len(test_cases)} test cases...")
        
        successful_tests = 0
        high_confidence_tests = 0
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. {test_case['description']}")
            print(f"   Input: '{test_case['input']}'")
            print(f"   Expected: {test_case['expected_intent']} (min confidence: {test_case['expected_confidence']})")
            
            # Test vector intent analysis
            analysis = router.get_intent_analysis(test_case['input'])
            
            detected_intent = analysis.get('vector_intent', 'UNKNOWN')
            confidence = analysis.get('confidence', 0.0)
            needs_feedback = analysis.get('needs_feedback', False)
            processing_method = analysis.get('processing_method', 'unknown')
            
            print(f"   Detected: {detected_intent} (confidence: {confidence:.4f})")
            print(f"   Needs feedback: {needs_feedback}")
            print(f"   Processing method: {processing_method}")
            
            # Check if detection is correct
            if detected_intent == test_case['expected_intent']:
                successful_tests += 1
                print("   ✅ Intent detection: CORRECT")
            else:
                print("   ❌ Intent detection: INCORRECT")
            
            # Check confidence level
            if confidence >= test_case['expected_confidence']:
                high_confidence_tests += 1
                print("   ✅ Confidence level: HIGH")
            else:
                print("   ⚠️ Confidence level: LOW")
            
            # Show top similarities
            if 'all_similarities' in analysis:
                top_3 = analysis.get('top_3_similarities', [])
                print("   Top 3 similarities:")
                for j, (intent, score) in enumerate(top_3[:3]):
                    print(f"     {j+1}. {intent}: {score:.4f}")
        
        # Test complete chatbot process
        print(f"\n🤖 Testing complete chatbot process...")
        test_input = "I want to find a Python developer job with good salary"
        user_id = "test_user_123"
        
        print(f"   Input: '{test_input}'")
        response_data = router.process_user_input(test_input, user_id, [])
        
        print(f"   Response: {response_data.get('message', 'No message')[:100]}...")
        print(f"   Vector Intent: {response_data.get('vector_intent', 'unknown')}")
        print(f"   Confidence: {response_data.get('vector_confidence', 0.0):.4f}")
        print(f"   Processing Method: {response_data.get('processing_method', 'unknown')}")
        
        # Summary
        print(f"\n📊 Test Results Summary:")
        print(f"   Total tests: {len(test_cases)}")
        print(f"   Successful intent detection: {successful_tests}/{len(test_cases)} ({successful_tests/len(test_cases)*100:.1f}%)")
        print(f"   High confidence tests: {high_confidence_tests}/{len(test_cases)} ({high_confidence_tests/len(test_cases)*100:.1f}%)")
        
        if successful_tests >= len(test_cases) * 0.8:  # 80% success rate
            print("   🎉 Overall result: PASSED")
        else:
            print("   ❌ Overall result: FAILED")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_similarity_scores():
    """Test specific similarity scores to ensure they meet the 0.98 requirement"""
    print("\n🔍 Testing High Similarity Scores")
    print("=" * 40)
    
    try:
        router = VectorChatbotRouter()
        
        # Test cases that should achieve very high similarity (>0.95)
        high_similarity_tests = [
            {
                "input": "Hello",
                "expected_intent": "GREETINGS",
                "min_score": 0.95
            },
            {
                "input": "Hi there",
                "expected_intent": "GREETINGS", 
                "min_score": 0.90
            },
            {
                "input": "Tell me about your company",
                "expected_intent": "ASK_COMPANY_INFO",
                "min_score": 0.85
            },
            {
                "input": "I want to apply for a job",
                "expected_intent": "ASK_JOB_OPPORTUNITIES",
                "min_score": 0.80
            }
        ]
        
        for test in high_similarity_tests:
            print(f"\n📝 Testing: '{test['input']}'")
            analysis = router.get_intent_analysis(test['input'])
            
            detected_intent = analysis.get('vector_intent', 'UNKNOWN')
            confidence = analysis.get('confidence', 0.0)
            
            print(f"   Expected: {test['expected_intent']} (min: {test['min_score']})")
            print(f"   Detected: {detected_intent} (score: {confidence:.4f})")
            
            if detected_intent == test['expected_intent'] and confidence >= test['min_score']:
                print("   ✅ HIGH SIMILARITY ACHIEVED")
            else:
                print("   ⚠️ Similarity below threshold")
        
        print("\n✅ Similarity score testing completed!")
        
    except Exception as e:
        print(f"❌ Error in similarity testing: {e}")
        import traceback
        traceback.print_exc()


def test_feedback_routing():
    """Test the feedback routing mechanism"""
    print("\n🔄 Testing Feedback Routing")
    print("=" * 30)
    
    try:
        router = VectorChatbotRouter()
        
        # Test cases that should require feedback
        feedback_tests = [
            "I want to upload my CV",
            "Can you review my resume?",
            "What jobs match my profile?",
            "What's the salary for AI engineers?"
        ]
        
        # Test cases that should NOT require feedback
        no_feedback_tests = [
            "Hello",
            "Tell me about your company",
            "Thank you",
            "I'm not interested"
        ]
        
        print("🔍 Testing feedback-required intents:")
        for test_input in feedback_tests:
            analysis = router.get_intent_analysis(test_input)
            needs_feedback = analysis.get('needs_feedback', False)
            processing_method = analysis.get('processing_method', 'unknown')
            
            print(f"   '{test_input}' -> Feedback: {needs_feedback}, Method: {processing_method}")
        
        print("\n🔍 Testing non-feedback intents:")
        for test_input in no_feedback_tests:
            analysis = router.get_intent_analysis(test_input)
            needs_feedback = analysis.get('needs_feedback', False)
            processing_method = analysis.get('processing_method', 'unknown')
            
            print(f"   '{test_input}' -> Feedback: {needs_feedback}, Method: {processing_method}")
        
        print("\n✅ Feedback routing testing completed!")
        
    except Exception as e:
        print(f"❌ Error in feedback routing test: {e}")


if __name__ == "__main__":
    print("🚀 Starting Vector Chatbot System Tests")
    print("=" * 60)
    
    # Run all tests
    test_vector_chatbot_system()
    test_similarity_scores()
    test_feedback_routing()
    
    print("\n🎉 All tests completed!")
    print("=" * 60)
