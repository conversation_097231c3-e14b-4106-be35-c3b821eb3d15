#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Context Manager cho <PERSON>ail <PERSON>bot
<PERSON><PERSON><PERSON><PERSON> lý context cuộc hội thoại
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime
import json
from intent_detector import IntentType, IntentResult


@dataclass
class ConversationTurn:
    """Một lượ<PERSON> hội thoại"""
    timestamp: datetime
    user_input: str
    intent: IntentType
    entities: Dict[str, str]
    bot_response: str
    confidence: float


@dataclass
class ConversationContext:
    """Context của cuộc hội thoại"""
    user_id: str
    conversation_turns: List[ConversationTurn] = field(default_factory=list)
    current_topic: Optional[str] = None
    user_profile: Dict[str, Any] = field(default_factory=dict)
    job_recommendations: List[Dict[str, Any]] = field(default_factory=list)
    last_job_discussed: Optional[Dict[str, Any]] = None
    
    def add_turn(self, turn: ConversationTurn):
        """Thêm một l<PERSON> hộ<PERSON> thoại"""
        self.conversation_turns.append(turn)
        self._update_context(turn)
    
    def _update_context(self, turn: ConversationTurn):
        """Cập nhật context dựa trên turn mới"""
        # Cập nhật topic hiện tại
        if turn.intent in [IntentType.ASK_JOB_OPPORTUNITIES, IntentType.ASK_JOB_DETAILS]:
            self.current_topic = "job_search"
        elif turn.intent == IntentType.ASK_COMPANY_INFO:
            self.current_topic = "company_info"
        elif turn.intent == IntentType.SHARE_PROFILE:
            self.current_topic = "profile_sharing"
        
        # Cập nhật user profile từ entities
        if 'skills' in turn.entities:
            self.user_profile['skills'] = turn.entities['skills']
        if 'position' in turn.entities:
            self.user_profile['desired_position'] = turn.entities['position']
        if 'salary_range' in turn.entities:
            self.user_profile['salary_expectation'] = turn.entities['salary_range']
    
    def get_last_turns(self, n: int = 3) -> List[ConversationTurn]:
        """Lấy n lượt hội thoại gần nhất"""
        return self.conversation_turns[-n:] if len(self.conversation_turns) >= n else self.conversation_turns
    
    def get_context_for_follow_up(self) -> str:
        """Tạo context string cho FOLLOW_UP intent"""
        if not self.conversation_turns:
            return "Chưa có cuộc hội thoại trước đó."
        
        last_turns = self.get_last_turns(2)
        context_parts = []
        
        for turn in last_turns:
            context_parts.append(f"User: {turn.user_input}")
            context_parts.append(f"Bot: {turn.bot_response}")
        
        return "\n".join(context_parts)


class ContextManager:
    """Quản lý context cho nhiều user"""
    
    def __init__(self):
        """Khởi tạo Context Manager"""
        self.contexts: Dict[str, ConversationContext] = {}
    
    def get_context(self, user_id: str) -> ConversationContext:
        """Lấy context của user"""
        if user_id not in self.contexts:
            self.contexts[user_id] = ConversationContext(user_id=user_id)
        return self.contexts[user_id]
    
    def add_conversation_turn(self, user_id: str, user_input: str, 
                            intent_result: IntentResult, bot_response: str):
        """Thêm một lượt hội thoại"""
        context = self.get_context(user_id)
        
        turn = ConversationTurn(
            timestamp=datetime.now(),
            user_input=user_input,
            intent=intent_result.intent,
            entities=intent_result.entities,
            bot_response=bot_response,
            confidence=intent_result.confidence
        )
        
        context.add_turn(turn)
    
    def has_context(self, user_id: str) -> bool:
        """Kiểm tra user có context không"""
        return user_id in self.contexts and len(self.contexts[user_id].conversation_turns) > 0
    
    def get_follow_up_context(self, user_id: str) -> str:
        """Lấy context cho FOLLOW_UP intent"""
        if not self.has_context(user_id):
            return ""
        
        context = self.get_context(user_id)
        return context.get_context_for_follow_up()
    
    def clear_context(self, user_id: str):
        """Xóa context của user"""
        if user_id in self.contexts:
            del self.contexts[user_id]
    
    def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """Lấy profile của user"""
        context = self.get_context(user_id)
        return context.user_profile
    
    def update_job_recommendations(self, user_id: str, jobs: List[Dict[str, Any]]):
        """Cập nhật job recommendations"""
        context = self.get_context(user_id)
        context.job_recommendations = jobs
        if jobs:
            context.last_job_discussed = jobs[0]  # Job đầu tiên
    
    def get_last_job_discussed(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Lấy job được thảo luận gần nhất"""
        context = self.get_context(user_id)
        return context.last_job_discussed
    
    def export_context(self, user_id: str) -> str:
        """Export context thành JSON"""
        if user_id not in self.contexts:
            return "{}"
        
        context = self.contexts[user_id]
        
        # Convert to serializable format
        data = {
            "user_id": context.user_id,
            "current_topic": context.current_topic,
            "user_profile": context.user_profile,
            "job_recommendations": context.job_recommendations,
            "last_job_discussed": context.last_job_discussed,
            "conversation_turns": [
                {
                    "timestamp": turn.timestamp.isoformat(),
                    "user_input": turn.user_input,
                    "intent": turn.intent.value,
                    "entities": turn.entities,
                    "bot_response": turn.bot_response,
                    "confidence": turn.confidence
                }
                for turn in context.conversation_turns
            ]
        }
        
        return json.dumps(data, ensure_ascii=False, indent=2)
    
    def import_context(self, user_id: str, json_data: str):
        """Import context từ JSON"""
        try:
            data = json.loads(json_data)
            context = ConversationContext(user_id=user_id)
            
            context.current_topic = data.get("current_topic")
            context.user_profile = data.get("user_profile", {})
            context.job_recommendations = data.get("job_recommendations", [])
            context.last_job_discussed = data.get("last_job_discussed")
            
            # Import conversation turns
            for turn_data in data.get("conversation_turns", []):
                turn = ConversationTurn(
                    timestamp=datetime.fromisoformat(turn_data["timestamp"]),
                    user_input=turn_data["user_input"],
                    intent=IntentType(turn_data["intent"]),
                    entities=turn_data["entities"],
                    bot_response=turn_data["bot_response"],
                    confidence=turn_data["confidence"]
                )
                context.conversation_turns.append(turn)
            
            self.contexts[user_id] = context
            return True
            
        except Exception as e:
            print(f"Lỗi import context: {str(e)}")
            return False


# Singleton instance
_context_manager = None

def get_context_manager() -> ContextManager:
    """Lấy singleton ContextManager instance"""
    global _context_manager
    if _context_manager is None:
        _context_manager = ContextManager()
    return _context_manager
