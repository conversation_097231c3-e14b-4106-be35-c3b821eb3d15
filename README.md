# 🤖 FOIS ICT PRO AI Chatbot - Mimi

**<PERSON>ệ thống trợ lý nhân sự thông minh đa kênh với AI tích hợp cho FOIS ICT PRO trên nền tảng Asiantech.link**

## 📋 Tổng quan

Mimi là hệ thống chatbot AI thông minh được phát triển để hỗ trợ tự động các hoạt động nhân sự của FOIS ICT PRO qua **2 kênh chính**:

### 📧 **Email Support Mode**
- Xử lý email tự động 24/7
- Phản hồi chuyên nghiệp qua email
- <PERSON><PERSON><PERSON> và phân tích CV đính kèm
- Prompt được tối ưu cho email formal

### 💬 **Web Chatbot Mode**
- Hỗ trợ trực tiếp trên website
- Tương tác real-time với visitors
- Giao diện chat thân thiện
- Prompt được tối ưu cho conversation

Cả 2 mode đề<PERSON> sử dụng AI Gemini 2.5 Flash với **cấu hình prompt riêng biệt** để đảm bảo phản hồi phù hợp với từng kênh.

## 🎯 Các dịch vụ hỗ trợ (Cả 2 mode)

### 🔍 **Tìm kiếm việc làm**
- **📧 Email**: Gửi danh sách job chi tiết với HTML formatting
- **💬 Web**: Tương tác real-time, filter jobs theo yêu cầu
- Lọc theo vị trí, công nghệ, địa điểm, mức lương
- Gợi ý vị trí phù hợp với kinh nghiệm

### 🏢 **Thông tin công ty**
- **📧 Email**: Thông tin đầy đủ về FOIS ICT PRO trong email formal
- **💬 Web**: Giới thiệu tương tác, Q&A về công ty
- Văn hóa làm việc và môi trường
- Các dự án và sản phẩm

### 💰 **Thông tin mức lương**
- **📧 Email**: Bảng lương chi tiết theo vị trí
- **💬 Web**: Tra cứu lương nhanh, so sánh positions
- Thông tin về benefits và phúc lợi
- Cơ hội tăng lương và thăng tiến

### 📋 **Hướng dẫn sử dụng platform**
- **📧 Email**: Hướng dẫn step-by-step qua email
- **💬 Web**: Hỗ trợ trực tiếp khi sử dụng website
- Tips tối ưu hóa hồ sơ
- Cách ứng tuyển hiệu quả

### 📄 **Hỗ trợ CV và ứng tuyển**
- **📧 Email**: **Đọc và phân tích CV tự động** (PDF, DOCX)
- **💬 Web**: Upload CV và nhận feedback instant
- Đánh giá điểm mạnh/yếu trong CV
- Gợi ý cải thiện hồ sơ chuyên nghiệp

### 🎯 **Tư vấn phát triển sự nghiệp**
- **📧 Email**: Career roadmap chi tiết qua email
- **💬 Web**: Tư vấn interactive, personalized advice
- Lộ trình phát triển theo từng vị trí
- Kỹ năng cần thiết cho từng level

### 📞 **Hướng dẫn quy trình phỏng vấn**
- **📧 Email**: Checklist và tips đầy đủ
- **💬 Web**: Mock interview, Q&A preparation
- Các bước trong quy trình tuyển dụng
- Chuẩn bị cho phỏng vấn

### 🌟 **Đánh giá và khuyến nghị kỹ năng**
- **📧 Email**: Skills assessment report
- **💬 Web**: Interactive skills quiz và recommendations
- Gợi ý kỹ năng cần phát triển
- Roadmap học tập cá nhân hóa

## 🚀 Tính năng nổi bật

### 🤖 **Dual-Mode AI System**
- **📧 Email Mode**: Formal, comprehensive responses
- **💬 Web Mode**: Conversational, interactive responses
- **Riêng biệt prompt config** cho từng mode
- Sử dụng Google Gemini 2.5 Flash

### 🌍 **Đa ngôn ngữ thông minh**
- **Tự động phát hiện ngôn ngữ** (Email & Web)
- Hỗ trợ **Tiếng Việt** và **Tiếng Anh**
- Phản hồi bằng chính ngôn ngữ người dùng
- Không trộn lẫn ngôn ngữ trong response

### 📧 **Email Features**
- **HTML email** với format chuyên nghiệp
- **CV processing** tự động (PDF, DOCX)
- **Attachment handling** thông minh
- **Professional tone** phù hợp business

### 💬 **Web Chatbot Features**
- **Real-time interaction** trên website
- **Conversational UI** thân thiện
- **Quick responses** cho user experience
- **Interactive elements** (buttons, suggestions)

### 🎯 **Smart AI Engine**
- **Intent Detection** chính xác 95%+
- **Context-aware responses**
- **Tone adaptation** theo từng mode
- **Conversation memory** thông minh

### 🔧 **Technical Excellence**
- **Token optimization** 90%+ efficiency
- **Multi-format support** (PDF, DOCX, HTML)
- **Error handling** robust
- **Performance monitoring** real-time

## 📊 Use Cases theo từng Mode

### 📧 **Email Support Examples**

#### 🔍 **Job Search via Email**
```
"Tôi muốn tìm công việc Python tại TP.HCM"
"I'm looking for Flutter developer position"
→ Response: Danh sách job chi tiết với HTML formatting
```

#### 📄 **CV Submission**
```
Email + đính kèm CV.pdf/CV.docx
"Tôi muốn ứng tuyển vào vị trí Backend Developer"
→ Response: CV analysis + job recommendations
```

#### 💰 **Salary Inquiry**
```
"Mức lương cho vị trí DevOps Engineer là bao nhiêu?"
→ Response: Bảng lương chi tiết theo experience level
```

### 💬 **Web Chatbot Examples**

#### 🔍 **Interactive Job Search**
```
User: "Tìm việc Python"
Bot: "Bạn muốn tìm vị trí nào? [Senior] [Mid] [Junior]"
User: Clicks "Senior"
Bot: "Địa điểm làm việc? [TP.HCM] [Hà Nội] [Remote]"
```

#### 🏢 **Company Q&A**
```
User: "FOIS ICT PRO làm gì?"
Bot: "FOIS ICT PRO chuyên về..."
User: "Văn hóa công ty thế nào?"
Bot: "Chúng tôi có văn hóa..."
```

#### 🎯 **Career Guidance**
```
User: "Làm sao để thành Senior Developer?"
Bot: "Roadmap để thành Senior Developer:
     1. Master core skills
     2. Lead projects
     3. Mentor juniors..."
```

## 🛠️ Kiến trúc hệ thống

### 🧠 **AI Core**
- **Engine**: Google Gemini 2.5 Flash
- **Dual Prompt System**: Riêng biệt cho Email & Web mode
- **Intent Detection**: Advanced NLP classification
- **Context Management**: Smart conversation memory

### 📧 **Email Module**
- **Protocols**: IMAP/SMTP with TLS/SSL
- **Document Processing**: PyPDF2, python-docx
- **HTML Generation**: Professional email formatting
- **Attachment Handling**: Automatic CV extraction

### 💬 **Web Chatbot Module**
- **Framework**: Flask/FastAPI for web integration
- **UI Components**: Interactive chat elements
- **Session Management**: User state tracking

### 🔧 **Technical Stack**
- **Language**: Python 3.12+
- **Database**: Context Manager với file storage
- **Configuration**: Environment variables
- **Monitoring**: Performance & error tracking


## 🔧 Cấu hình hệ thống

### 📧 **Email Mode Configuration**
```python
# Email Settings
SMTP_SETTINGS = {
    "SMTP_SERVER": "smtp.gmail.com",
    "SMTP_PORT": 587,
    "SENDER_MAIL": "<EMAIL>",
    "PASSWORD": "your-app-password"
}

IMAP_SETTINGS = {
    "IMAP_SERVER": "imap.gmail.com",
    "IMAP_PORT": 993
}

# Email-specific prompt config
EMAIL_PROMPT_CONFIG = {
    "tone": "professional",
    "format": "html",
    "include_services_list": True,
    "cv_analysis": True
}
```

### 💬 **Web Chatbot Configuration**
```python
# Web Settings
WEB_SETTINGS = {
    "HOST": "0.0.0.0",
    "PORT": 5000,
    "WEBSOCKET_ENABLED": True,
    "SESSION_TIMEOUT": 3600
}

# Web-specific prompt config
WEB_PROMPT_CONFIG = {
    "tone": "conversational",
    "format": "markdown",
    "interactive_elements": True,
    "quick_responses": True
}
```

### 🤖 **AI Settings (Shared)**
```python
GEMINI_API_KEY = "your-gemini-api-key"
CHATBOT_SETTINGS = {
    'USE_AI': True,
    'EMAIL_MODE': True,
    'WEB_MODE': True,
    'CHECK_INTERVAL': 30,
    'TEST_EMAIL': '<EMAIL>'
}
```

## 🔄 Workflow hoạt động

### 📧 **Email Mode Workflow**
```mermaid
graph TD
    A[📧 Email đến] --> B{Phát hiện ngôn ngữ}
    B -->|Tiếng Việt| C[🇻🇳 Email Prompt VN]
    B -->|English| D[🇺🇸 Email Prompt EN]

    A --> E{Có CV đính kèm?}
    E -->|Có| F[📄 Đọc & phân tích CV]
    E -->|Không| G[📝 Xử lý email thông thường]

    F --> H[🤖 AI phân tích profile]
    G --> I[🎯 Intent Detection]
    H --> J[💼 Gợi ý vị trí phù hợp]
    I --> K[📧 Tạo HTML response]
    J --> K
    K --> L[✅ Gửi email phản hồi]
```

### 💬 **Web Chatbot Workflow**
```mermaid
graph TD
    A[💬 User message] --> B{Phát hiện ngôn ngữ}
    B -->|Tiếng Việt| C[🇻🇳 Web Prompt VN]
    B -->|English| D[🇺🇸 Web Prompt EN]

    A --> E[🎯 Intent Detection]
    E --> F{Loại tương tác}
    F -->|Job Search| G[🔍 Interactive job filter]
    F -->|Company Info| H[🏢 Q&A conversation]
    F -->|Career Advice| I[🎯 Personalized guidance]

    G --> J[💬 Conversational response]
    H --> J
    I --> J
    J --> K[✅ Real-time reply]
```


## 📞 Liên hệ hỗ trợ

- **Email**: <EMAIL>
- **Website**: https://asiantech.link
- **Company**: FOIS ICT PRO
- **Platform**: Asiantech.link

## 🎉 Kết luận

Mimi - AI Chatbot System của FOIS ICT PRO là giải pháp **đa kênh** toàn diện cho việc tự động hóa các hoạt động nhân sự. Với **2 mode hoạt động riêng biệt**:


**Kết quả**: Nâng cao trải nghiệm ứng viên qua cả email và web, tối ưu hóa quy trình tuyển dụng của FOIS ICT PRO với AI thông minh.

---
*Được phát triển bởi FOIS ICT PRO với ❤️ | Powered by Google Gemini AI*
*Dual-Mode Architecture: Email Support + Web Chatbot*
