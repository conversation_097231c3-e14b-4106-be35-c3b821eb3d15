#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Intent Detection Module cho Email Chatbot
Xử lý 11 loại intent khác nhau
"""

import re
import json
from enum import Enum
from typing import Dict, List, Optional
from dataclasses import dataclass


class IntentType(Enum):
    """Enum cho các loại intent"""
    GREETINGS = "GREETINGS"
    ASK_COMPANY_INFO = "ASK_COMPANY_INFO"
    ASK_PLATFORM_USAGE = "ASK_PLATFORM_USAGE"
    ASK_JOB_OPPORTUNITIES = "ASK_JOB_OPPORTUNITIES"
    ASK_JOB_DETAILS = "ASK_JOB_DETAILS"
    SHARE_PROFILE = "SHARE_PROFILE"
    NOT_INTERESTED = "NOT_INTERESTED"
    SALARY_EXPECTATION = "SALARY_EXPECTATION"
    FEEDBACK_ON_JOB_MATCH = "FEEDBACK_ON_JOB_MATCH"
    FOLLOW_UP = "FOLLOW_UP"
    OTHER = "OTHER"


@dataclass
class IntentResult:
    """Kết quả phân tích intent"""
    intent: IntentType
    confidence: float
    entities: Dict[str, str]
    context_needed: bool = False


class IntentDetector:
    """Class phát hiện intent từ text sử dụng AI"""

    def __init__(self, gemini_ai=None):
        """Khởi tạo Intent Detector với AI"""
        self.gemini_ai = gemini_ai
        self.intent_descriptions = self._load_intent_descriptions()
        self.follow_up_indicators = self._load_follow_up_indicators()

        # Fallback patterns cho trường hợp AI không khả dụng
        self.fallback_patterns = self._load_fallback_patterns()

    def _load_intent_descriptions(self) -> Dict[IntentType, str]:
        """Load mô tả chi tiết cho từng intent để AI hiểu"""
        return {
            IntentType.GREETINGS: """
Chào hỏi đơn thuần, lời chào xã giao.
Ví dụ: "Xin chào", "Hello", "Chào bạn", "Hi", "Good morning"
""",

            IntentType.ASK_COMPANY_INFO: """
Hỏi về thông tin công ty FOIS ICT PRO/FOIS ICT PRO, muốn biết công ty làm gì, giới thiệu công ty.
Ví dụ: "FOIS là gì?", "Công ty làm gì?", "Giới thiệu về công ty", "FOIS ICT PRO hoạt động như thế nào?", "AsianTech là gì?"
""",

            IntentType.ASK_PLATFORM_USAGE: """
Hỏi cách sử dụng hệ thống, hướng dẫn sử dụng website/app, cách thức hoạt động của platform.
Ví dụ: "Cách sử dụng hệ thống", "Hướng dẫn dùng app", "Làm sao để dùng web này?", "How to use this platform?"
""",

            IntentType.ASK_JOB_OPPORTUNITIES: """
Tìm kiếm cơ hội việc làm, muốn xem danh sách job, tìm việc theo kỹ năng/vị trí.
Ví dụ: "Tìm job Python", "Có việc làm developer không?", "Muốn xem job opportunities", "Tuyển dụng gì?"
""",

            IntentType.ASK_JOB_DETAILS: """
Hỏi chi tiết về một job cụ thể, yêu cầu công việc, mô tả job, qualification.
Ví dụ: "Chi tiết job này", "Job requirements là gì?", "Mô tả công việc", "Yêu cầu vị trí này"
""",

            IntentType.SHARE_PROFILE: """
Muốn gửi CV, chia sẻ hồ sơ cá nhân, ứng tuyển vào vị trí, submit profile.
Ví dụ: "Gửi CV", "Tôi muốn ứng tuyển", "Đây là hồ sơ của tôi", "Apply for this job"
""",

            IntentType.NOT_INTERESTED: """
Từ chối nhẹ nhàng, không quan tâm tìm việc lúc này, không muốn tiếp tục.
Ví dụ: "Không quan tâm", "Tôi không muốn tìm việc", "No thanks", "Not interested"
""",

            IntentType.SALARY_EXPECTATION: """
Hỏi về mức lương, thu nhập, salary range, compensation của vị trí nào đó.
Ví dụ: "Mức lương bao nhiêu?", "Salary range", "Thu nhập developer", "Lương Python Developer"
""",

            IntentType.FEEDBACK_ON_JOB_MATCH: """
Phản hồi rằng job không phù hợp, muốn tìm job khác, không thích job được gợi ý.
Ví dụ: "Job này không phù hợp", "Tôi không thích job này", "Có job khác không?", "Not suitable for me"
""",

            IntentType.FOLLOW_UP: """
Câu hỏi tiếp theo trong cuộc hội thoại, thường ngắn gọn, thiếu context đầy đủ, cần tham chiếu đến cuộc trò chuyện trước.
Dấu hiệu: có từ nối (vậy, thế, còn, nữa), thiếu chủ ngữ rõ ràng, tham chiếu đến thứ đã nói trước đó.
Ví dụ: "Vậy tiếp theo sao?", "Cái job đó có remote không?", "Còn gì nữa?", "Thế lương bao nhiêu?"
""",

            IntentType.OTHER: """
Các câu hỏi ngoài phạm vi tuyển dụng/việc làm, không liên quan đến công ty hay platform.
Ví dụ: "Thời tiết hôm nay", "Tôi thích ăn phở", "Bóng đá", "Tin tức"
"""
        }

    def _load_fallback_patterns(self) -> Dict[IntentType, List[str]]:
        """Load các pattern cho từng intent"""
        return {
            IntentType.GREETINGS: [
                r'\b(xin chào|chào|hello|hi|hey)\b',
                r'\b(chào bạn|chào anh|chào chị)\b',
                r'\b(good morning|good afternoon|good evening)\b'
            ],

            IntentType.ASK_COMPANY_INFO: [
                r'\b(asiantechlink|asian tech|công ty)\b.*\b(là gì|giới thiệu|thông tin)\b',
                r'\b(về công ty|company info|about company)\b',
                r'\b(công ty làm gì|hoạt động gì)\b'
            ],

            IntentType.ASK_PLATFORM_USAGE: [
                r'\b(cách dùng|hướng dẫn|sử dụng)\b.*\b(web|app|hệ thống|platform)\b',
                r'\b(làm sao để|how to)\b.*\b(dùng|use)\b',
                r'\b(hướng dẫn sử dụng|user guide)\b'
            ],

            IntentType.ASK_JOB_OPPORTUNITIES: [
                r'\b(tìm việc|job|công việc|vị trí)\b',
                r'\b(có việc|có job|có vị trí)\b.*\b(nào|gì|không)\b',
                r'\b(muốn xem|xem job|job opportunities)\b',
                r'\b(tuyển dụng|recruitment|hiring)\b',
                r'\b(muốn tìm|tìm kiếm)\b.*\b(việc|job)\b',
                r'\b(xem.*việc làm|việc làm.*developer)\b'
            ],

            IntentType.ASK_JOB_DETAILS: [
                r'\b(chi tiết|details|thông tin)\b.*\b(job|việc|vị trí)\b',
                r'\b(job.*yêu cầu|requirements|qualification)\b',
                r'\b(mô tả công việc|job description)\b'
            ],

            IntentType.SHARE_PROFILE: [
                r'\b(gửi cv|send cv|cv|resume)\b',
                r'\b(hồ sơ|profile|thông tin cá nhân)\b',
                r'\b(ứng tuyển|apply|application)\b'
            ],

            IntentType.NOT_INTERESTED: [
                r'\b(không quan tâm|not interested|không muốn)\b',
                r'\b(không tìm việc|không cần|no thanks)\b',
                r'\b(từ chối|decline|pass)\b'
            ],

            IntentType.SALARY_EXPECTATION: [
                r'\b(lương|salary|mức lương|pay)\b',
                r'\b(thu nhập|income|compensation)\b',
                r'\b(bao nhiêu|how much|range)\b.*\b(lương|salary)\b'
            ],

            IntentType.FEEDBACK_ON_JOB_MATCH: [
                r'\b(job.*không phù hợp|not suitable|not match)\b',
                r'\b(không thích|don\'t like|not interested in)\b.*\b(job|việc)\b',
                r'\b(job.*khác|việc.*khác|other.*job)\b',
                r'\b(không phù hợp|not suitable)\b'
            ]
        }

    def _load_follow_up_indicators(self) -> List[str]:
        """Load các indicator cho FOLLOW_UP intent"""
        return [
            r'\b(vậy|thế|thì|còn|nữa|tiếp theo)\b',
            r'\b(cái.*kia|job.*đó|việc.*đó)\b',
            r'\b(có.*không|có.*gì|sao|nhỉ)\b',
            r'\b(à đúng rồi|ờ|ừm|hmm)\b'
        ]

    def detect_intent(self, text: str, has_context: bool = False) -> IntentResult:
        """
        Phát hiện intent từ text sử dụng AI

        Args:
            text: Nội dung cần phân tích
            has_context: Có context cuộc hội thoại trước không

        Returns:
            IntentResult: Kết quả phân tích intent
        """
        text_lower = text.lower().strip()

        # Kiểm tra FOLLOW_UP trước nếu có context (vẫn dùng rule-based)
        if has_context and self._is_follow_up_simple(text_lower):
            return IntentResult(
                intent=IntentType.FOLLOW_UP,
                confidence=0.9,
                entities=self._extract_follow_up_entities(text_lower),
                context_needed=True
            )

        # Sử dụng AI để detect intent
        if self.gemini_ai and self.gemini_ai.is_available():
            try:
                ai_result = self._detect_intent_with_ai(text)
                if ai_result:
                    return ai_result
            except Exception as e:
                print(f"⚠️ AI intent detection failed: {str(e)}")

        # Fallback về pattern matching
        print("📝 Fallback to pattern matching")
        return self._detect_intent_fallback(text_lower)

    def _detect_intent_with_ai(self, text: str) -> IntentResult:
        """Sử dụng AI để detect intent với structured output"""

        # Tạo prompt cho AI
        intent_list = []
        for intent_type, description in self.intent_descriptions.items():
            intent_list.append(
                f"**{intent_type.value}**: {description.strip()}")

        prompt = f"""
Bạn là một AI chuyên phân loại ý định (intent) của người dùng trong hệ thống tuyển dụng.

DANH SÁCH CÁC INTENT:
{chr(10).join(intent_list)}

NHIỆM VỤ:
Phân tích câu sau và xác định intent phù hợp nhất: "{text}"

YÊU CẦU PHẢN HỒI:
Trả lời CHÍNH XÁC theo format JSON sau:
{{
    "intent": "TÊN_INTENT",
    "confidence": 0.95,
    "entities": {{
        "skills": "python, react",
        "position": "developer",
        "salary_range": "20-30 triệu",
        "location": "Hà Nội"
    }},
    "reasoning": "Lý do ngắn gọn"
}}

LƯU Ý:
- intent phải là một trong: {', '.join([i.value for i in IntentType])}
- confidence từ 0.0 đến 1.0
- entities chỉ trích xuất nếu có (skills, position, salary_range, location, question_type, reference_type)
- reasoning giải thích ngắn gọn tại sao chọn intent này
"""

        try:
            # Sử dụng structured output
            result = self.gemini_ai.generate_structured_content(prompt)

            # Validate intent
            intent_value = result.get('intent', 'OTHER')
            try:
                intent_type = IntentType(intent_value)
            except ValueError:
                intent_type = IntentType.OTHER

            confidence = float(result.get('confidence', 0.5))
            entities = result.get('entities', {})
            reasoning = result.get('reasoning', '')

            print(
                f"🤖 AI detected: {intent_type.value} (confidence: {confidence:.2f})")
            if reasoning:
                print(f"💭 Reasoning: {reasoning}")

            return IntentResult(
                intent=intent_type,
                confidence=confidence,
                entities=entities,
                context_needed=(intent_type == IntentType.FOLLOW_UP)
            )

        except Exception as e:
            print(f"❌ Error with AI structured detection: {str(e)}")
            return None

    def _detect_intent_fallback(self, text: str) -> IntentResult:
        """Fallback detection sử dụng pattern matching"""
        best_intent = IntentType.OTHER
        best_confidence = 0.0
        entities = {}

        for intent_type, patterns in self.fallback_patterns.items():
            confidence = self._calculate_confidence(text, patterns)
            if confidence > best_confidence:
                best_confidence = confidence
                best_intent = intent_type
                entities = self._extract_entities(text, intent_type)

        # Nếu confidence thấp, coi là OTHER
        if best_confidence < 0.3:
            best_intent = IntentType.OTHER
            best_confidence = 0.5

        return IntentResult(
            intent=best_intent,
            confidence=best_confidence,
            entities=entities,
            context_needed=(best_intent == IntentType.FOLLOW_UP)
        )

    def _is_follow_up_simple(self, text: str) -> bool:
        """Kiểm tra có phải FOLLOW_UP intent không"""
        # Kiểm tra độ dài câu (FOLLOW_UP thường ngắn)
        if len(text.split()) > 15:
            return False

        # Kiểm tra có follow-up indicators
        follow_up_score = 0
        for pattern in self.follow_up_indicators:
            if re.search(pattern, text):
                follow_up_score += 1

        # Kiểm tra thiếu chủ ngữ rõ ràng
        lacks_subject = not any(word in text for word in [
            'tôi', 'mình', 'em', 'anh', 'chị', 'i want', 'i need'
        ])

        if lacks_subject:
            follow_up_score += 1

        return follow_up_score >= 2

    def _calculate_confidence(self, text: str, patterns: List[str]) -> float:
        """Tính confidence score cho intent"""
        matches = 0
        total_patterns = len(patterns)

        for pattern in patterns:
            if re.search(pattern, text):
                matches += 1

        return matches / total_patterns if total_patterns > 0 else 0.0

    def _extract_entities(self, text: str, intent_type: IntentType) -> Dict[str, str]:
        """Trích xuất entities từ text theo intent"""
        entities = {}

        if intent_type == IntentType.ASK_JOB_OPPORTUNITIES:
            # Trích xuất skill, position
            skills = re.findall(
                r'\b(python|java|javascript|react|nodejs|backend|frontend|fullstack|devops)\b', text)
            if skills:
                entities['skills'] = ', '.join(skills)

            positions = re.findall(
                r'\b(developer|engineer|programmer|designer|tester|manager)\b', text)
            if positions:
                entities['position'] = ', '.join(positions)

        elif intent_type == IntentType.SALARY_EXPECTATION:
            # Trích xuất số tiền
            salary_numbers = re.findall(
                r'\b(\d+(?:\.\d+)?)\s*(triệu|million|k|nghìn)\b', text)
            if salary_numbers:
                entities['salary_range'] = str(salary_numbers[0])

        return entities

    def _extract_follow_up_entities(self, text: str) -> Dict[str, str]:
        """Trích xuất entities cho FOLLOW_UP intent"""
        entities = {}

        # Trích xuất từ khóa tham chiếu
        if 'job' in text or 'việc' in text:
            entities['reference_type'] = 'job'

        if 'remote' in text:
            entities['question_type'] = 'remote'
        elif 'yêu cầu' in text or 'requirement' in text:
            entities['question_type'] = 'requirements'
        elif 'lương' in text or 'salary' in text:
            entities['question_type'] = 'salary'

        return entities


# Factory function
def create_intent_detector(gemini_ai=None) -> IntentDetector:
    """Tạo Intent Detector instance với AI"""
    return IntentDetector(gemini_ai)
