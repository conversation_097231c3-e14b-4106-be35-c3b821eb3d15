# ===================================
# CORE DEPENDENCIES (Required)
# ===================================

# Web framework
flask==3.0.0
flask-cors==4.0.0
werkzeug>=2.0.0

# Environment and configuration
python-dotenv==1.1.1

# HTTP requests
requests==2.31.0

# AI/ML libraries (Required for vector chatbot)
google-generativeai>=0.8.5
google-genai
numpy==2.3.1

# Type hints support
typing-extensions>=4.0.0

# Date/time handling
python-dateutil>=2.8.0

# ===================================
# CV PROCESSING (Optional)
# Install only if you need CV upload features
# Skip these if you only want basic chat functionality
# ===================================

# Document processing
PyPDF2==3.0.1
python-docx==1.1.0
pdfplumber>=0.9.0

# Image processing
Pillow>=9.0.0

# File type detection
python-magic==0.4.27

# ===================================
# PRODUCTION DEPLOYMENT (Optional)
# Install only for production deployment
# ===================================

# Production WSGI server (eliminates Flask dev warnings)
waitress>=2.1.0

# ===================================
# ENHANCED FEATURES (Optional)
# Install only if you need advanced features
# ===================================

# Enhanced file type detection
# filetype>=1.2.0

# Advanced image processing
# opencv-python>=4.5.0

# Advanced document parsing
# textract>=1.6.0

# ===================================
# INSTALLATION GUIDE
# ===================================
#
# MINIMAL INSTALL (Core chatbot only):
# pip install flask flask-cors python-dotenv requests google-generativeai google-genai numpy typing-extensions python-dateutil
#
# STANDARD INSTALL (Recommended - includes CV features):
# pip install -r requirements.txt
#
# PRODUCTION INSTALL (All features):
# pip install -r requirements.txt
# Uncomment optional packages above as needed