#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vector-based Intent Detection using Gemini Embeddings
"""

import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import google.generativeai as genai
from typing import Dict, List, Tuple
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class VectorIntentDetector:
    """Vector-based intent detection using embeddings"""

    def __init__(self):
        """Initialize the vector intent detector"""
        # Configure Gemini API
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError(
                "GEMINI_API_KEY not found in environment variables")

        genai.configure(api_key=api_key)

        # Define intent descriptions for vector calculation
        self.intent_descriptions = {
            "GREETINGS": [
                "Hello, hi, good morning, good afternoon, good evening, greetings, hey there",
                "<PERSON><PERSON><PERSON> b<PERSON><PERSON>, <PERSON><PERSON> ch<PERSON>, ch<PERSON><PERSON> bu<PERSON><PERSON>, ch<PERSON><PERSON> bu<PERSON><PERSON> chiề<PERSON>, chào buổi tối",
                "Friendly greeting and welcoming messages"
            ],
            "ASK_COMPANY_INFO": [
                "Tell me about the company, company information, what does your company do",
                "Thông tin về công ty, công ty làm gì, giới thiệu về công ty FOIS",
                "Questions about company background, services, history, and general information"
            ],
            "ASK_PLATFORM_USAGE": [
                "How to use this platform, how does this system work, user guide",
                "Hướng dẫn sử dụng, cách dùng hệ thống, hướng dẫn platform",
                "Questions about how to use the chatbot platform and its features"
            ],
            "ASK_JOB_OPPORTUNITIES": [
                "Job openings, available positions, career opportunities, hiring",
                "Cơ hội việc làm, vị trí tuyển dụng, tuyển dụng, việc làm",
                "Questions about job opportunities, open positions, and career prospects"
            ],
            "ASK_JOB_DETAILS": [
                "Job requirements, job description, salary, benefits, job details",
                "Yêu cầu công việc, mô tả công việc, lương, phúc lợi, chi tiết việc làm",
                "Specific questions about job requirements, responsibilities, and compensation"
            ],
            "SHARE_PROFILE": [
                "My CV, my resume, my experience, my skills, my background",
                "CV của tôi, kinh nghiệm của tôi, kỹ năng của tôi, hồ sơ cá nhân",
                "Sharing personal profile, CV, resume, or professional background"
            ],
            "NOT_INTERESTED": [
                "Not interested, no thanks, not suitable, decline, reject",
                "Không quan tâm, không phù hợp, từ chối, không muốn",
                "Expressing lack of interest or declining opportunities"
            ],
            "SALARY_EXPECTATION": [
                "Salary expectation, expected salary, compensation, pay, wage",
                "Mong muốn lương, lương mong đợi, mức lương, thu nhập",
                "Questions about salary expectations and compensation discussions"
            ],
            "FEEDBACK_ON_JOB_MATCH": [
                "Job match feedback, suitable position, good fit, perfect match",
                "Phản hồi về công việc phù hợp, vị trí phù hợp, công việc tốt",
                "Feedback about job matching and suitability of positions"
            ],
            "FOLLOW_UP": [
                "Follow up, next steps, what's next, continue conversation",
                "Theo dõi, bước tiếp theo, tiếp tục, hỏi thêm",
                "Follow-up questions and continuing previous conversations"
            ],
            "OTHER": [
                "General questions, miscellaneous, other topics, random questions",
                "Câu hỏi chung, chủ đề khác, câu hỏi ngẫu nhiên",
                "Other questions that don't fit into specific categories"
            ]
        }

        # Cache for intent vectors
        self.intent_vectors = {}
        self._initialize_intent_vectors()

    def get_embedding(self, text: str) -> np.ndarray:
        """Get embedding vector for text using Gemini"""
        try:
            response = genai.embed_content(
                model="models/text-embedding-004",
                content=text,
                task_type="RETRIEVAL_DOCUMENT"
            )
            return np.array(response['embedding'])
        except Exception as e:
            print(f"Error getting embedding: {e}")
            # Return zero vector as fallback
            return np.zeros(768)  # Default embedding size

    def cosine_similarity_score(self, vector1: np.ndarray, vector2: np.ndarray) -> float:
        """Calculate cosine similarity between two vectors"""
        try:
            # Ensure vectors are 2D for sklearn
            vec1 = vector1.reshape(1, -1)
            vec2 = vector2.reshape(1, -1)
            return cosine_similarity(vec1, vec2)[0][0]
        except Exception as e:
            print(f"Error calculating cosine similarity: {e}")
            return 0.0

    def _initialize_intent_vectors(self):
        """Initialize vectors for all intent descriptions"""
        print("🔄 Initializing intent vectors...")

        for intent, descriptions in self.intent_descriptions.items():
            # Combine all descriptions for this intent
            combined_description = " ".join(descriptions)

            # Get embedding vector
            vector = self.get_embedding(combined_description)
            self.intent_vectors[intent] = vector

            print(f"✅ {intent}: vector shape {vector.shape}")

        print("🎉 Intent vectors initialized successfully!")

    def detect_intent_vector(self, user_input: str) -> Dict[str, float]:
        """Detect intent using vector similarity"""
        # Get embedding for user input
        user_vector = self.get_embedding(user_input)

        # Calculate similarity with each intent
        similarities = {}

        for intent, intent_vector in self.intent_vectors.items():
            similarity = self.cosine_similarity_score(
                user_vector, intent_vector)
            similarities[intent] = similarity

        return similarities

    def get_best_intent(self, user_input: str, threshold: float = 0.3) -> Tuple[str, float, Dict[str, float]]:
        """Get the best matching intent with confidence score"""
        similarities = self.detect_intent_vector(user_input)

        # Find the best match
        best_intent = max(similarities, key=similarities.get)
        best_score = similarities[best_intent]

        # If best score is below threshold, classify as OTHER
        if best_score < threshold:
            best_intent = "OTHER"
            best_score = similarities.get("OTHER", 0.0)

        return best_intent, best_score, similarities


def test_similarity():
    """Unit test function for similarity calculation"""
    print("🧪 Testing Similarity Function")
    print("=" * 40)

    # Initialize detector
    detector = VectorIntentDetector()

    # Test cases
    test_cases = [
        {
            "input": "Hello friend",
            "expected_top": "GREETINGS",
            "description": "Simple greeting"
        },
        {
            "input": "Tell me about FOIS ICT PRO company",
            "expected_top": "ASK_COMPANY_INFO",
            "description": "Company information request"
        },
        {
            "input": "What job opportunities do you have?",
            "expected_top": "ASK_JOB_OPPORTUNITIES",
            "description": "Job opportunities inquiry"
        },
        {
            "input": "Here is my CV and experience",
            "expected_top": "SHARE_PROFILE",
            "description": "Profile sharing"
        },
        {
            "input": "What is my expected salary?",
            "expected_top": "SALARY_EXPECTATION",
            "description": "Salary discussion"
        },
        {
            "input": "Goodbye, see you later",
            "expected_top": "OTHER",  # Since we don't have FAREWELL
            "description": "Farewell message"
        }
    ]

    print("🎯 Running Test Cases:")
    print("-" * 25)

    for i, test_case in enumerate(test_cases, 1):
        user_input = test_case["input"]
        expected = test_case["expected_top"]
        description = test_case["description"]

        print(f"\n{i}. {description}")
        print(f"   Input: '{user_input}'")

        # Get intent detection results
        best_intent, best_score, all_similarities = detector.get_best_intent(
            user_input)

        print(f"   Best Intent: {best_intent} (score: {best_score:.4f})")
        print(f"   Expected: {expected}")

        # Show top 3 similarities
        sorted_similarities = sorted(
            all_similarities.items(), key=lambda x: x[1], reverse=True)
        print("   Top 3 similarities:")
        for intent, score in sorted_similarities[:3]:
            print(f"     - {intent}: {score:.4f}")

        # Check if prediction matches expectation
        if best_intent == expected:
            print("   ✅ PASS")
        else:
            print("   ⚠️ DIFFERENT (but may still be valid)")

    print("\n" + "=" * 40)
    print("🎉 Similarity test completed!")

    return detector


if __name__ == "__main__":
    print("🚀 Vector-based Intent Detection System")
    print("=" * 50)

    # Run the similarity test
    detector = test_similarity()

    print("\n💡 Usage Example:")
    print("-" * 15)

    # Interactive example
    example_input = "Hello, how are you today?"
    best_intent, confidence, all_scores = detector.get_best_intent(
        example_input)

    print(f"Input: '{example_input}'")
    print(f"Detected Intent: {best_intent}")
    print(f"Confidence: {confidence:.4f}")
    print("\nAll Scores:")
    for intent, score in sorted(all_scores.items(), key=lambda x: x[1], reverse=True):
        print(f"  {intent}: {score:.4f}")
